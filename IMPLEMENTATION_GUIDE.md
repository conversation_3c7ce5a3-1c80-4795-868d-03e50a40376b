# Local vs International Products Implementation Guide

## Overview
This implementation adds support for distinguishing between local and international products in the e-commerce system, with appropriate shipping options for each type.

## What's Been Implemented

### 1. Admin Interface for Courier Services
- **Location**: `/admin/courier-services`
- **Features**:
  - List all international courier services
  - Add new courier services with pricing and country support
  - Edit existing courier services
  - Delete courier services
  - Filter and search functionality

### 2. Database Schema
- **courier_services table**: Stores international shipping providers
- **product_shipping table**: Links products to their shipping configuration
- **Sample Data**: Pre-populated with DHL, FedEx, and UPS services

### 3. Product Classification
- Products now show "Local" or "International" badges
- Badge color: Green for Local, Blue for International
- Classification based on shipping configuration in `product_shipping` table

### 4. Product Filtering
- Added shipping type filter to products page
- Users can filter by "Local Only" or "International Only"
- Filter persists in URL parameters

### 5. Enhanced API
- Updated product API to include `isLocal` property
- Added courier services management endpoints
- Proper joining with shipping information

## Setup Instructions

### 1. Database Setup
Run the SQL script to create the necessary tables:

```bash
# Execute the database setup script
psql -d your_database_name -f database-setup.sql
```

Or copy the contents of `database-setup.sql` and run it in your Supabase SQL editor.

### 2. Admin Access
1. Navigate to `/admin/courier-services`
2. Add courier services for international shipping
3. Configure pricing, delivery times, and supported countries

### 3. Product Configuration
For each product, you need to set up shipping information:

1. **For Local Products**:
   - Set `shipping_type = 'local'`
   - Assign a `delivery_service_id` from `external_delivery_services`

2. **For International Products**:
   - Set `shipping_type = 'international'`
   - Assign a `courier_service_id` from `courier_services`

### 4. Testing the Implementation

#### Test Product Badges
1. Go to `/products`
2. You should see green "Local" or blue "International" badges on products
3. Use the filter to show only local or international products

#### Test Admin Interface
1. Go to `/admin/courier-services`
2. Add a new courier service
3. Edit existing services
4. Check that the services appear in product forms

#### Test Shipping Logic
1. Products with local shipping show green badge
2. Products with international shipping show blue badge
3. Default products (no shipping config) show as local

## File Changes Made

### New Files Created:
1. `/admin/courier-services/page.tsx` - Admin list page
2. `/admin/courier-services/new/page.tsx` - Add new service
3. `/admin/courier-services/[id]/page.tsx` - View service details
4. `/admin/courier-services/[id]/edit/page.tsx` - Edit service
5. `/api/admin/courier-services/route.ts` - API endpoints
6. `/api/admin/courier-services/[id]/route.ts` - Individual service API
7. `database-setup.sql` - Database schema and sample data

### Modified Files:
1. `AdminLayout.tsx` - Added courier services to navigation
2. `ProductCard.tsx` - Enhanced badge display
3. `products/page.tsx` - Added shipping type filtering
4. `products/api-client.ts` - Added isLocal property calculation

## Features Included

### Admin Features:
- ✅ Courier services management
- ✅ Pricing configuration (base cost + per kg)
- ✅ Country support selection
- ✅ Delivery time estimates
- ✅ Tracking support toggle

### User Features:
- ✅ Visual product classification (badges)
- ✅ Shipping type filtering
- ✅ Responsive design
- ✅ URL-based filter persistence

### Backend Features:
- ✅ Database constraints for data integrity
- ✅ Proper indexing for performance
- ✅ Sample data population
- ✅ API validation and error handling

## Next Steps (Optional Enhancements)

1. **Enhanced Checkout**:
   - Show different shipping options based on product types in cart
   - Calculate international shipping costs automatically
   - Address validation for international orders

2. **Product Management**:
   - Add shipping configuration to product admin forms
   - Bulk update shipping types
   - Import/export shipping configurations

3. **Customer Experience**:
   - Shipping cost calculator on product pages
   - Delivery time estimates
   - Country-specific product availability

4. **Analytics**:
   - Track local vs international sales
   - Shipping cost analysis
   - Popular destination countries

## Troubleshooting

### Products Show Wrong Badge
- Check `product_shipping` table for correct entries
- Verify `shipping_type` is set to 'local' or 'international'
- Ensure foreign key relationships are correct

### Admin Interface Not Showing
- Verify navigation was updated in `AdminLayout.tsx`
- Check user has proper admin permissions
- Ensure API endpoints are accessible

### Filter Not Working
- Check URL parameters are being passed correctly
- Verify API client handles `shippingType` filter
- Ensure database joins include shipping information

## API Endpoints

### Courier Services Admin API:
- `GET /api/admin/courier-services` - List services
- `POST /api/admin/courier-services` - Create service
- `GET /api/admin/courier-services/[id]` - Get service
- `PUT /api/admin/courier-services/[id]` - Update service
- `DELETE /api/admin/courier-services/[id]` - Delete service

### Product API Enhancements:
- Added `isLocal` property to product responses
- Added `shippingType` filter parameter
- Enhanced with shipping information joins

This implementation provides a solid foundation for managing local vs international products with appropriate shipping options for each type.# Local vs International Products Implementation Guide

## Overview
This implementation adds support for distinguishing between local and international products in the e-commerce system, with appropriate shipping options for each type.

## What's Been Implemented

### 1. Admin Interface for Courier Services
- **Location**: `/admin/courier-services`
- **Features**:
  - List all international courier services
  - Add new courier services with pricing and country support
  - Edit existing courier services
  - Delete courier services
  - Filter and search functionality

### 2. Database Schema
- **courier_services table**: Stores international shipping providers
- **product_shipping table**: Links products to their shipping configuration
- **Sample Data**: Pre-populated with DHL, FedEx, and UPS services

### 3. Product Classification
- Products now show "Local" or "International" badges
- Badge color: Green for Local, Blue for International
- Classification based on shipping configuration in `product_shipping` table

### 4. Product Filtering
- Added shipping type filter to products page
- Users can filter by "Local Only" or "International Only"
- Filter persists in URL parameters

### 5. Enhanced API
- Updated product API to include `isLocal` property
- Added courier services management endpoints
- Proper joining with shipping information

## Setup Instructions

### 1. Database Setup
Run the SQL script to create the necessary tables:

```bash
# Execute the database setup script
psql -d your_database_name -f database-setup.sql
```

Or copy the contents of `database-setup.sql` and run it in your Supabase SQL editor.

### 2. Admin Access
1. Navigate to `/admin/courier-services`
2. Add courier services for international shipping
3. Configure pricing, delivery times, and supported countries

### 3. Product Configuration
For each product, you need to set up shipping information:

1. **For Local Products**:
   - Set `shipping_type = 'local'`
   - Assign a `delivery_service_id` from `external_delivery_services`

2. **For International Products**:
   - Set `shipping_type = 'international'`
   - Assign a `courier_service_id` from `courier_services`

### 4. Testing the Implementation

#### Test Product Badges
1. Go to `/products`
2. You should see green "Local" or blue "International" badges on products
3. Use the filter to show only local or international products

#### Test Admin Interface
1. Go to `/admin/courier-services`
2. Add a new courier service
3. Edit existing services
4. Check that the services appear in product forms

#### Test Shipping Logic
1. Products with local shipping show green badge
2. Products with international shipping show blue badge
3. Default products (no shipping config) show as local

## File Changes Made

### New Files Created:
1. `/admin/courier-services/page.tsx` - Admin list page
2. `/admin/courier-services/new/page.tsx` - Add new service
3. `/admin/courier-services/[id]/page.tsx` - View service details
4. `/admin/courier-services/[id]/edit/page.tsx` - Edit service
5. `/api/admin/courier-services/route.ts` - API endpoints
6. `/api/admin/courier-services/[id]/route.ts` - Individual service API
7. `database-setup.sql` - Database schema and sample data

### Modified Files:
1. `AdminLayout.tsx` - Added courier services to navigation
2. `ProductCard.tsx` - Enhanced badge display
3. `products/page.tsx` - Added shipping type filtering
4. `products/api-client.ts` - Added isLocal property calculation

## Features Included

### Admin Features:
- ✅ Courier services management
- ✅ Pricing configuration (base cost + per kg)
- ✅ Country support selection
- ✅ Delivery time estimates
- ✅ Tracking support toggle

### User Features:
- ✅ Visual product classification (badges)
- ✅ Shipping type filtering
- ✅ Responsive design
- ✅ URL-based filter persistence

### Backend Features:
- ✅ Database constraints for data integrity
- ✅ Proper indexing for performance
- ✅ Sample data population
- ✅ API validation and error handling

## Next Steps (Optional Enhancements)

1. **Enhanced Checkout**:
   - Show different shipping options based on product types in cart
   - Calculate international shipping costs automatically
   - Address validation for international orders

2. **Product Management**:
   - Add shipping configuration to product admin forms
   - Bulk update shipping types
   - Import/export shipping configurations

3. **Customer Experience**:
   - Shipping cost calculator on product pages
   - Delivery time estimates
   - Country-specific product availability

4. **Analytics**:
   - Track local vs international sales
   - Shipping cost analysis
   - Popular destination countries

## Troubleshooting

### Products Show Wrong Badge
- Check `product_shipping` table for correct entries
- Verify `shipping_type` is set to 'local' or 'international'
- Ensure foreign key relationships are correct

### Admin Interface Not Showing
- Verify navigation was updated in `AdminLayout.tsx`
- Check user has proper admin permissions
- Ensure API endpoints are accessible

### Filter Not Working
- Check URL parameters are being passed correctly
- Verify API client handles `shippingType` filter
- Ensure database joins include shipping information

## API Endpoints

### Courier Services Admin API:
- `GET /api/admin/courier-services` - List services
- `POST /api/admin/courier-services` - Create service
- `GET /api/admin/courier-services/[id]` - Get service
- `PUT /api/admin/courier-services/[id]` - Update service
- `DELETE /api/admin/courier-services/[id]` - Delete service

### Product API Enhancements:
- Added `isLocal` property to product responses
- Added `shippingType` filter parameter
- Enhanced with shipping information joins

This implementation provides a solid foundation for managing local vs international products with appropriate shipping options for each type.