# Finder Project - Complete Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Project Structure](#project-structure)
3. [Technology Stack](#technology-stack)
4. [Database Schema](#database-schema)
5. [Features & Functionality](#features--functionality)
6. [Development Timeline](#development-timeline)
7. [Admin System](#admin-system)
8. [File Organization](#file-organization)
9. [Development Conventions](#development-conventions)
10. [Recent Fixes & Updates](#recent-fixes--updates)
11. [Deployment & Scripts](#deployment--scripts)

---

## Project Overview

**Finder** is a comprehensive e-commerce marketplace platform built with Next.js 15, featuring:
- Multi-vendor marketplace functionality
- Advanced product search and filtering
- Deal management system
- Comprehensive admin panel
- Store owner dashboards
- Payment processing (Wave, Crypto)
- Real-time notifications
- Order management system

---

## Project Structure

### Root Directory Structure
```
finder/
├── nextjs/                    # Main Next.js application
│   ├── src/
│   │   ├── app/              # Next.js App Router pages
│   │   ├── components/       # Reusable UI components
│   │   ├── features/         # Feature-based modules
│   │   ├── lib/             # Utilities and services
│   │   ├── types/           # TypeScript type definitions
│   │   └── utils/           # Helper functions
│   ├── public/              # Static assets
│   └── package.json         # Next.js dependencies
├── supabase/                # Database and backend
│   ├── migrations/          # Database migrations
│   ├── scripts/            # Database scripts and utilities
│   ├── *.sql               # SQL schema files (30 files)
│   └── config.toml         # Supabase configuration
├── package.json            # Root project dependencies
└── README.md              # Project documentation
```

### Feature-Based Organization

Each feature in `src/features/` contains:
- `types.ts`: Type definitions for the feature
- `api.ts`: Server-side API functions
- `api-client.ts`: Client-side API functions
- `queries.ts`: TanStack React Query hooks
- `components/`: UI components specific to the feature

### Main Features
- **Products**: Product listings, details, filtering, and search
- **Categories**: Category listings and filtering
- **Stores**: Store listings, details, and products by store
- **Cart**: Shopping cart functionality
- **Checkout**: Checkout process with payment options
- **User**: User authentication, profile, and settings
- **Wishlist**: User wishlist functionality
- **Orders**: Order history and tracking
- **Deals**: Deal management and promotions
- **Admin**: Administrative functions and dashboards

---

## Technology Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: TanStack React Query
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React

### Backend & Database
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Real-time**: Supabase Realtime
- **Storage**: Supabase Storage
- **API**: Next.js API Routes

### Payment Processing
- **Wave**: QR code payments
- **Crypto**: Cryptocurrency payments

### Development Tools
- **Package Manager**: npm/yarn
- **Linting**: ESLint
- **Type Checking**: TypeScript
- **Build Tool**: Next.js built-in

---

## Database Schema

### Core Tables

#### Products Table
```sql
CREATE TABLE IF NOT EXISTS products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  price DECIMAL(10, 2) NOT NULL,
  compare_at_price DECIMAL(10, 2),
  currency TEXT NOT NULL DEFAULT 'GMD',
  category_id UUID REFERENCES categories(id),
  store_id UUID REFERENCES stores(id),
  featured BOOLEAN NOT NULL DEFAULT false,
  trending BOOLEAN NOT NULL DEFAULT false,
  in_stock BOOLEAN NOT NULL DEFAULT true,
  rating DECIMAL(3, 2) DEFAULT 0,
  review_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Categories Table
```sql
CREATE TABLE IF NOT EXISTS categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  image TEXT,
  parent_id UUID REFERENCES categories(id),
  featured BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Stores Table
```sql
CREATE TABLE IF NOT EXISTS stores (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  logo TEXT,
  cover_image TEXT,
  owner_id UUID REFERENCES auth.users(id),
  contact_email TEXT,
  contact_phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  country TEXT,
  postal_code TEXT,
  rating DECIMAL(3, 2) DEFAULT 0,
  review_count INTEGER DEFAULT 0,
  featured BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Deals Table
```sql
CREATE TABLE IF NOT EXISTS deals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  product_id UUID REFERENCES products(id),
  deal_type TEXT NOT NULL CHECK (deal_type IN ('flash', 'clearance', 'seasonal', 'regular')),
  original_price DECIMAL(10, 2) NOT NULL,
  deal_price DECIMAL(10, 2) NOT NULL,
  discount_percentage INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'GMD',
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  max_quantity INTEGER,
  used_quantity INTEGER DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT true,
  featured BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Additional Tables
- `orders`: Order management
- `order_items`: Order line items
- `cart_items`: Shopping cart items
- `profiles`: User profiles and roles
- `product_images`: Product image management
- `reviews`: Product and store reviews
- `notifications`: User notifications
- `feature_toggles`: Admin feature management
- `payouts`: Store owner payouts

---

## Features & Functionality

### Customer Features
- **Product Browsing**: Advanced search, filtering, and categorization
- **Shopping Cart**: Add/remove items, quantity management
- **Wishlist**: Save products for later
- **User Accounts**: Registration, login, profile management
- **Order Management**: Order history, tracking, status updates
- **Reviews & Ratings**: Product and store reviews
- **Deals & Promotions**: Flash sales, clearance, seasonal deals
- **Payment Options**: Wave QR codes, cryptocurrency

### Store Owner Features
- **Store Management**: Store profile, branding, contact information
- **Product Management**: Add, edit, delete products
- **Order Processing**: View and manage incoming orders
- **Analytics**: Sales reports, performance metrics
- **Payout Management**: Track earnings and payouts

### Admin Features
- **User Management**: Manage all users and roles
- **Store Management**: Approve/manage all stores
- **Product Oversight**: Monitor all products across stores
- **Deal Management**: Create and manage marketplace deals
- **Order Management**: Oversee all orders and disputes
- **Analytics Dashboard**: Platform-wide metrics
- **Feature Toggles**: Control feature access by role
- **System Settings**: Platform configuration

---

## Development Timeline

### Phase 1: Foundation ✅
- Set up project structure
- Implement TanStack React Query
- Create feature-based folder organization
- Set up mock data services

### Phase 2: Core Features ✅
- Implement product listings and details
- Implement category pages with filtering
- Implement store pages
- Set up search functionality with indexing
- Implement pagination

### Phase 3: User Features ✅
- Implement user authentication
- Implement user profile and settings
- Implement wishlist functionality
- Implement cart functionality

### Phase 4: Checkout and Orders ✅
- Implement checkout flow
- Implement Wave QR code payment option
- Implement crypto payment option
- Implement order status indicators
- Implement order history

### Phase 5: Store Management ✅
- Implement store dashboard
- Implement product management for store owners
- Implement order management for store owners

---

## Admin System

### Consolidated Admin Interface

The admin system has been consolidated from separate interfaces into a unified system:

**Previous System:**
- `/admin` - For super admins
- `/store-admin` - For store owners

**Current System:**
- `/admin` - Unified interface for both admins and store owners
- Feature toggles control access based on user roles

### Feature Toggle System

| Feature Key | Description | Default Roles |
|-------------|-------------|---------------|
| `store_management` | Store creation and management | admin |
| `product_management` | Product creation and management | admin, store_owner |
| `order_management` | Order processing and management | admin, store_owner |
| `payout_management` | Payout processing and management | admin, store_owner |
| `analytics_view` | Analytics and reporting access | admin, store_owner |
| `user_management` | User management access | admin |
| `synergy_store` | Synergy store management | admin |
| `settings_access` | System settings access | admin, store_owner |

### Admin Pages Structure
```
/admin/
├── dashboard/          # Main admin dashboard
├── products/          # Product management
├── categories/        # Category management
├── stores/           # Store management
├── users/            # User management
├── orders/           # Order management
├── deals/            # Deal management
├── payouts/          # Payout management
├── settings/         # System settings & feature toggles
└── analytics/        # Analytics and reports
```

---

## File Organization

### Database Files (Organized ✅)
All SQL and script files have been organized into the `supabase/` directory:

**SQL Files (30 files):**
- Located in `/supabase/` root
- Include schema definitions, migrations, and fixes
- Examples: `order_workflow_schema.sql`, `multi_role_system.sql`, `enhanced_order_system.sql`

**Script Files:**
- Located in `/supabase/scripts/`
- Include deployment and utility scripts
- Files: `apply-migrations.js`, `apply-schema-to-cloud.js`, `fix_code.sh`

### Node Modules Structure
The project has two `node_modules` directories (this is normal):
- **Root `node_modules`**: Basic project dependencies and tools
- **`nextjs/node_modules`**: Main Next.js application dependencies

This is a standard monorepo-style setup where the Next.js app is in a subdirectory.

---

## Development Conventions

### Currency
- Use **Dalasis (GMD)** as the default currency instead of Dollars

### API Response Format
All API responses follow a consistent format:
```typescript
{
  data?: T;
  error?: string;
}
```

### Query Keys (TanStack React Query)
Use consistent query key structure:
```typescript
const queryKeys = {
  all: ['resourceName'] as const,
  lists: () => [...queryKeys.all, 'list'] as const,
  list: (params) => [...queryKeys.lists(), params] as const,
  details: () => [...queryKeys.all, 'detail'] as const,
  detail: (id) => [...queryKeys.details(), id] as const,
};
```

### Component Props
Use consistent prop naming:
```typescript
interface ComponentProps {
  // Required props first
  id: string;
  name: string;
  // Optional props next
  className?: string;
  onClick?: () => void;
}
```

### File Naming Conventions
- **Components**: PascalCase (e.g., `ProductCard.tsx`)
- **Pages**: lowercase with hyphens (e.g., `product-details.tsx`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Types**: PascalCase (e.g., `Product.ts`)

---

## Recent Fixes & Updates

### Deal Deletion Issue Fixed ✅

**Problem:** Deleted deals were still visible on the frontend due to fallback logic.

**Solution Implemented:**
1. **Enhanced Admin Deal Deletion** (`/features/admin/api.ts`):
   - Added proper validation and error handling
   - Reset product's `compare_at_price` to prevent fallback deals
   - Improved logging and feedback

2. **Improved Frontend DealsService** (`/lib/services/deals.ts`):
   - Added null checks for products
   - Restricted fallback logic to prevent showing deleted deals
   - Better query filtering

3. **Enhanced Admin UI** (`/app/admin/deals/page.tsx`):
   - Immediate UI feedback (deal disappears instantly)
   - Delayed refresh for database consistency
   - Better user experience

### File Organization Completed ✅
- All SQL files moved to `/supabase/`
- All script files moved to `/supabase/scripts/`
- Empty directories cleaned up
- Improved project structure

### Admin System Consolidation ✅
- Unified admin interface for all user types
- Feature toggle system for role-based access
- Improved navigation and user experience

---

## Deployment & Scripts

### Database Scripts
Located in `/supabase/scripts/`:

- **`apply-migrations.js`**: Apply database migrations
- **`apply-schema-to-cloud.js`**: Deploy schema to Supabase cloud
- **`fix_code.sh`**: Code fixing utilities

### Environment Setup
1. **Local Development**: Use Supabase CLI for local development
2. **Production**: Deploy to Supabase cloud with provided scripts
3. **Environment Variables**: Configure in `.env.local`

### Key Commands
```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Apply database migrations
node supabase/scripts/apply-migrations.js

# Deploy schema to cloud
node supabase/scripts/apply-schema-to-cloud.js
```

---

## Security & Permissions

### Row Level Security (RLS)
- All database tables have RLS policies
- Role-based access control
- User can only access their own data

### Authentication
- Supabase Auth integration
- JWT token-based authentication
- Role-based permissions (admin, store_owner, customer)

### API Security
- Server-side validation
- Protected API routes
- Input sanitization and validation

---

## Performance Optimizations

### Frontend
- Next.js App Router for optimal performance
- Image optimization with Next.js Image component
- Code splitting and lazy loading
- TanStack React Query for efficient data fetching

### Database
- Proper indexing on frequently queried columns
- Optimized queries with joins
- Connection pooling
- Query result caching

---

## Monitoring & Analytics

### Built-in Analytics
- Product view tracking
- Order conversion metrics
- User engagement analytics
- Store performance metrics

### Error Monitoring
- Client-side error tracking
- Server-side error logging
- Database query monitoring

---

## Future Enhancements

### Planned Features
1. **Mobile App**: React Native mobile application
2. **Advanced Analytics**: More detailed reporting and insights
3. **Multi-language Support**: Internationalization
4. **Advanced Search**: Elasticsearch integration
5. **Social Features**: User reviews, ratings, social sharing
6. **Marketing Tools**: Email campaigns, promotional codes
7. **Inventory Management**: Advanced stock tracking
8. **Shipping Integration**: Third-party shipping providers

### Technical Improvements
1. **Performance**: Further optimization and caching
2. **Testing**: Comprehensive test suite
3. **Documentation**: API documentation and guides
4. **CI/CD**: Automated deployment pipeline
5. **Monitoring**: Advanced monitoring and alerting

---

## Support & Maintenance

### Code Quality
- TypeScript for type safety
- ESLint for code consistency
- Prettier for code formatting
- Comprehensive error handling

### Documentation
- Inline code comments
- API documentation
- User guides and tutorials
- Developer setup instructions

### Version Control
- Git-based version control
- Feature branch workflow
- Code review process
- Automated testing on commits

---

*Last Updated: December 2024*
*Project Status: Active Development*
*Version: 1.0.0*