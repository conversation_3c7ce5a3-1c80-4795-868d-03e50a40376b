# Database and Code Fix Scripts

This directory contains scripts to fix database schema and code issues related to the cart functionality.

## Issues Addressed

1. **Missing 'options' column in cart table**
   - Error: `"Could not find the 'options' column of 'cart' in the schema cache"`
   - The code expects an 'options' column in the cart table, but it doesn't exist in the current schema.

2. **UUID conversion errors**
   - Error: `"invalid input syntax for type uuid: \"NaN\""`
   - This happens because the code is trying to convert product IDs to numbers using `Number()`, but the database is using UUIDs.

3. **NULL value constraint violation**
   - Error: `"null value in column \"product_id\" of relation \"cart\" violates not-null constraint"`
   - This occurs when the product_id column is set to NULL during the type conversion process.

## Database Scripts

### 1. Schema Application

Apply the complete database schema to your Supabase cloud instance:

```bash
# Navigate to the nextjs directory and run:
cd nextjs
node ../supabase/scripts/apply-schema-to-cloud.js
```

This script will:
- Create the exec_sql function if needed
- Apply the main finder_schema.sql
- Apply all migration files in order
- Set up all necessary tables and relationships

### 2. Migration Application

Apply individual migration files:

```bash
# Navigate to the nextjs directory and run:
cd nextjs
node ../supabase/scripts/apply-migrations.js
```

This script will:
- Apply all SQL files in the migrations directory
- Handle errors gracefully and continue with remaining migrations
- Provide detailed feedback on success/failure

## Verification

After running the schema application, verify your setup:

1. Check your Supabase Dashboard:
   - Go to "Table Editor"
   - Verify tables exist: orders, order_items, payments, profiles, stores, products, etc.

2. Test the admin panel:
   - Visit http://localhost:3000/admin/orders
   - Should load without "Order Not Found" errors

3. Check API endpoints:
   - Visit http://localhost:3000/api/admin/debug
   - Should show that tables exist

- The fix scripts ensure compatibility with the UUID-based schema, which appears to be the one currently in use.

- If you encounter any issues after applying these fixes, you may need to check for other code that assumes product IDs are numbers rather than UUIDs.
