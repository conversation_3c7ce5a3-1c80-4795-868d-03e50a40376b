import { createSPASassClient } from '@/lib/supabase/client';
import { Database } from '@/lib/types';
import {
  Product,
  Category,
  CartItem,
  WishlistItem,
  Order,
  Review
} from '@/lib/types/ecommerce';

export class EcommerceClientService {
  // Cart
  static async getCart(userId: string): Promise<CartItem[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('cart')
        .select(`
          *,
          product:products(
            *,
            store:stores(*),
            category:categories(*),
            images:product_images(*)
          )
        `)
        .eq('user_id', userId);

      if (error) throw error;

      // Convert the Supabase data to CartItem format
      const cartItems: CartItem[] = (data || []).map(item => ({
        id: item.id,
        created_at: item.created_at,
        user_id: item.user_id,
        product_id: item.product_id,
        quantity: item.quantity,
        options: item.options as Record<string, any> | null,
        product: item.product ? {
          id: item.product.id,
          name: item.product.name,
          slug: item.product.slug,
          description: item.product.description,
          price: item.product.price,
          compare_at_price: item.product.compare_at_price,
          currency: item.product.currency,
          featured: item.product.featured,
          inStock: item.product.in_stock || true,
          is_local: item.product.is_local ?? true, // Include is_local field for checkout logic
          rating: item.product.rating,
          images: item.product.images || [],
          store: item.product.store,
          category: item.product.category
        } : undefined
      }));

      return cartItems;
    } catch (error) {
      console.error('Error fetching cart:', error);
      return [];
    }
  }
  // Helper function to validate UUID
  private static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  static async addToCart(productId: string | number, quantity = 1, options: Record<string, any> | null = null): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Ensure productId is a valid string (UUID)
    const productIdStr = String(productId);

    // Validate the UUID
    if (!this.isValidUUID(productIdStr)) {
      console.error('Invalid product ID format:', productIdStr);
      throw new Error('Invalid product ID format');
    }

    // Check if item already exists in cart
    try {
      const { data: existingItem, error: fetchError } = await supabase
        .from('cart')
        .select('*')
        .eq('user_id', user.user.id)
        .eq('product_id', productIdStr)
        .single();

      if (fetchError) {
        console.log('[EcommerceClientService addToCart] Raw fetchError from .single():', JSON.stringify(fetchError, null, 2));
        // A PostgrestError from .single() due to 0 rows with Accept: application/vnd.pgrst.object+json
        // should result in a PostgrestError object with code 'PGRST116'.
        // We treat this as "item not found" and proceed to insert.
        // Any other error code, or if it's not a PostgrestError with a code, should be thrown.
        if (typeof fetchError === 'object' && fetchError !== null && 'code' in fetchError) {
          if (fetchError.code !== 'PGRST116') {
            console.error('Error checking cart (not PGRST116):', fetchError);
            throw fetchError;
          }
          // If code IS 'PGRST116', it's "0 rows found", which is expected. Do nothing, proceed to insert.
        } else {
          // Not a standard PostgrestError with a code property, or null.
          console.error('Unexpected error type or null error when checking cart:', fetchError);
          throw fetchError; // Or handle as appropriate
        }
      }

      if (existingItem) {
        // Update quantity
        const { error } = await supabase
          .from('cart')
          .update({
            quantity: existingItem.quantity + quantity,
            options,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingItem.id);

        if (error) {
          console.error('Error updating cart:', error);
          throw error;
        }
      } else {
        // Insert new item
        const { error } = await supabase
          .from('cart')
          .insert({
            user_id: user.user.id,
            product_id: productIdStr,
            quantity,
            options,
            updated_at: new Date().toISOString()
          });

        if (error) {
          console.error('Error adding to cart:', error);
          throw error;
        }
      }
    } catch (error) {
      console.error('Cart operation failed:', error);
      throw error;
    }
  }

  static async updateCartItem(cartItemId: string, quantity: number): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Validate the UUID
    if (!this.isValidUUID(cartItemId)) {
      console.error('Invalid cart item ID format:', cartItemId);
      throw new Error('Invalid cart item ID format');
    }

    try {
      const { error } = await supabase
        .from('cart')
        .update({
          quantity,
          updated_at: new Date().toISOString()
        })
        .eq('id', cartItemId);

      if (error) {
        console.error('Error updating cart item:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to update cart item:', error);
      throw error;
    }
  }

  static async removeFromCart(cartItemId: string): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Validate the UUID
    if (!this.isValidUUID(cartItemId)) {
      console.error('Invalid cart item ID format:', cartItemId);
      throw new Error('Invalid cart item ID format');
    }

    try {
      const { error } = await supabase
        .from('cart')
        .delete()
        .eq('id', cartItemId);

      if (error) {
        console.error('Error removing cart item:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to remove cart item:', error);
      throw error;
    }
  }

  // Wishlist
  static async addToWishlist(productId: string | number): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('wishlist')
      .insert({
        user_id: user.user.id,
        product_id: productId
      });

    if (error && error.code !== '23505') throw error; // Ignore unique constraint violations
  }

  static async removeFromWishlist(wishlistItemId: string | number): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { error } = await supabase
      .from('wishlist')
      .delete()
      .eq('id', wishlistItemId);

    if (error) throw error;
  }

  // Reviews
  static async addReview(
    productId: number,
    rating: number,
    title: string | null = null,
    content: string | null = null
  ): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('reviews')
      .insert({
        user_id: user.user.id,
        product_id: productId,
        rating,
        title,
        content
      });

    if (error) throw error;
  }

  // Client-side data fetching (for client components)
  static async getProducts(options: {
    limit?: number;
    category_id?: number | string;
    featured?: boolean;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  } = {}): Promise<Product[]> {
    const {
      limit = 10,
      category_id,
      featured,
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = options;

    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      let query = supabase
        .from('products')
        .select(`
          *,
          store:stores(*),
          category:categories(*),
          images:product_images(*)
        `)
        .limit(limit);

      if (category_id) {
        query = query.eq('category_id', category_id);
      }

      if (featured !== undefined) {
        query = query.eq('featured', featured);
      }

      if (search) {
        query = query.ilike('name', `%${search}%`);
      }

      // Apply sorting
      query = query.order(sort_by, { ascending: sort_order === 'asc' });

      const { data, error } = await query;

      if (error) throw error;

      // Transform data to ensure inStock is properly set
      const transformedData = (data || []).map(product => ({
        ...product,
        // Always set products as in stock
        inStock: true
      }));

      return transformedData;
    } catch (error) {
      console.error('Error fetching products:', error);
      return [];
    }
  }

  static async getFeaturedProducts(limit = 4): Promise<Product[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          store:stores(*),
          category:categories(*),
          images:product_images(*)
        `)
        .eq('featured', true)
        .limit(limit);

      if (error) throw error;

      // Transform data to ensure inStock is properly set
      const transformedData = (data || []).map(product => ({
        ...product,
        // Always set products as in stock
        inStock: true
      }));

      return transformedData;
    } catch (error) {
      console.error('Error fetching featured products:', error);
      return [];
    }
  }

  static async getCategories(): Promise<Category[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }

  static async getFeaturedCategories(limit = 4): Promise<Category[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('featured', true)
        .order('name')
        .limit(limit);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching featured categories:', error);
      return [];
    }
  }

  static async getStores(limit = 6): Promise<any[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('stores')
        .select('*')
        .order('name')
        .limit(limit);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching stores:', error);
      return [];
    }
  }
}
