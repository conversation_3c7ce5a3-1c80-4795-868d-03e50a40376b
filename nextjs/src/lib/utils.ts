import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateRandomString(length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = '';
  const charsetLength = charset.length;

  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charsetLength));
  }

  return result;
}

/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'GMD')
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: string = 'GMD',
  options: {
    notation?: Intl.NumberFormatOptions['notation'];
    maximumFractionDigits?: number;
    minimumFractionDigits?: number;
  } = {}
): string {
  const {
    notation = 'standard',
    maximumFractionDigits = 2,
    minimumFractionDigits = 2,
  } = options;

  // For GMD (Dalasis), use the custom formatter
  if (currency === 'GMD') {
    return new Intl.NumberFormat('en-GM', {
      style: 'currency',
      currency: 'GMD',
      notation,
      maximumFractionDigits,
      minimumFractionDigits,
    }).format(amount);
  }

  // For other currencies, use their respective formatters
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency,
    notation,
    maximumFractionDigits,
    minimumFractionDigits,
  }).format(amount);
}