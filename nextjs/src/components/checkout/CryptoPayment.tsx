import React, { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Copy, CheckCircle, Bitcoin } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface CryptoPaymentProps {
  amount: number;
  currency: string;
  selectedCrypto: string;
  onCryptoChange: (crypto: string) => void;
  onProceed: () => void;
}

// This would be fetched from your backend in a real implementation
const cryptoOptions = [
  { 
    id: 'bitcoin', 
    name: 'Bitcoin (BTC)', 
    symbol: 'BTC',
    address: '**********************************',
    qrCode: '/images/btc-qr-placeholder.png',
    color: 'orange',
    description: 'The original cryptocurrency'
  },
  { 
    id: 'ethereum', 
    name: 'Ether<PERSON> (ETH)', 
    symbol: 'ETH',
    address: '******************************************',
    qrCode: '/images/eth-qr-placeholder.png',
    color: 'blue',
    description: 'Smart contract platform'
  },
  { 
    id: 'usdt', 
    name: 'Tether (USDT)', 
    symbol: 'USDT',
    address: 'TKrV3XpMgMEfG668QL7hdpvM4vwrxBnNwh',
    qrCode: '/images/usdt-qr-placeholder.png',
    color: 'green',
    description: 'USD-pegged stablecoin'
  }
];

const CryptoPayment: React.FC<CryptoPaymentProps> = ({ 
  amount, 
  currency, 
  selectedCrypto: selectedCryptoId, 
  onCryptoChange, 
  onProceed 
}) => {
  const [copied, setCopied] = useState(false);
  
  const selectedCrypto = cryptoOptions.find(crypto => crypto.id === selectedCryptoId) || cryptoOptions[0];
  
  // In a real implementation, you would convert the amount to the selected cryptocurrency
  const cryptoAmount = amount; // This would be converted based on current exchange rates

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(selectedCrypto.address);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleSelectCrypto = (value: string) => {
    onCryptoChange(value);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">Pay with Cryptocurrency</h2>
      
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Please send the equivalent of {formatCurrency(amount, currency)} in cryptocurrency to our wallet.
        </AlertDescription>
      </Alert>
      
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Select Cryptocurrency
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {cryptoOptions.map(crypto => {
            const isSelected = selectedCrypto.id === crypto.id;
            const getColorClasses = (color: string, isSelected: boolean) => {
              if (color === 'orange') {
                return {
                  border: isSelected ? 'border-orange-500 bg-orange-50 ring-2 ring-orange-200' : 'border-gray-200 hover:border-orange-300 hover:bg-orange-25',
                  iconBg: 'bg-orange-100',
                  iconColor: 'text-orange-600',
                  textColor: isSelected ? 'text-orange-900' : 'text-gray-900'
                };
              } else if (color === 'blue') {
                return {
                  border: isSelected ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' : 'border-gray-200 hover:border-blue-300 hover:bg-blue-25',
                  iconBg: 'bg-blue-100',
                  iconColor: 'text-blue-600',
                  textColor: isSelected ? 'text-blue-900' : 'text-gray-900'
                };
              } else {
                return {
                  border: isSelected ? 'border-green-500 bg-green-50 ring-2 ring-green-200' : 'border-gray-200 hover:border-green-300 hover:bg-green-25',
                  iconBg: 'bg-green-100',
                  iconColor: 'text-green-600',
                  textColor: isSelected ? 'text-green-900' : 'text-gray-900'
                };
              }
            };
            
            const colorClasses = getColorClasses(crypto.color, isSelected);
            
            return (
              <div
                key={crypto.id}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${colorClasses.border}`}
                onClick={() => handleSelectCrypto(crypto.id)}
              >
                <div className="text-center">
                  <div className={`w-12 h-12 rounded-full ${colorClasses.iconBg} flex items-center justify-center mx-auto mb-3`}>
                    <Bitcoin className={`h-6 w-6 ${colorClasses.iconColor}`} />
                  </div>
                  <h3 className={`font-semibold text-sm ${colorClasses.textColor}`}>{crypto.symbol}</h3>
                  <p className="text-xs text-gray-500 mt-1">{crypto.description}</p>
                  {isSelected && (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        ✓ Selected
                      </span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Selected Crypto Info */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-900">Selected: {selectedCrypto.name}</p>
              <p className="text-xs text-gray-500">Network: {selectedCrypto.symbol} Mainnet</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">≈ {formatCurrency(cryptoAmount, currency)}</p>
              <p className="text-xs text-gray-500">Estimated value</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex flex-col items-center">
        <div className="mb-6 p-4 border border-gray-200 rounded-lg">
          {/* This is a placeholder. In production, use your actual crypto QR code */}
          <div className="relative w-64 h-64 mx-auto bg-gray-100 flex items-center justify-center">
            <p className="text-gray-500 text-sm">{selectedCrypto.name} QR Code Placeholder</p>
            {/* Uncomment when you have the actual QR code image */}
            {/* <Image
              src={selectedCrypto.qrCode}
              alt={`${selectedCrypto.name} QR Code`}
              fill
              className="object-contain"
            /> */}
          </div>
        </div>
        
        <div className="w-full max-w-md mb-6">
          <p className="text-center font-medium mb-2">Wallet Address:</p>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
            <span className="font-mono text-xs sm:text-sm truncate max-w-[200px] sm:max-w-[300px]">
              {selectedCrypto.address}
            </span>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleCopyAddress}
              className="flex items-center gap-1 flex-shrink-0"
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-500">Copied</span>
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  <span>Copy</span>
                </>
              )}
            </Button>
          </div>
        </div>
        
        <div className="w-full max-w-md space-y-4">
          <div className="p-4 bg-orange-50 rounded-md">
            <h3 className="font-medium text-orange-800 mb-2">Payment Instructions:</h3>
            <ol className="list-decimal pl-5 text-orange-700 space-y-1">
              <li>Open your cryptocurrency wallet</li>
              <li>Scan the QR code above or copy the wallet address</li>
              <li>Send the equivalent of {formatCurrency(amount, currency)}</li>
              <li>After payment, click "I've Paid" below</li>
              <li>You'll need to provide your transaction ID/hash</li>
            </ol>
          </div>
          
          <Button 
            onClick={onProceed}
            className="w-full bg-primary-600 hover:bg-primary-700"
          >
            I've Paid
          </Button>
          
          <p className="text-center text-sm text-gray-500">
            Please only click "I've Paid" after you have completed the payment.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CryptoPayment;
