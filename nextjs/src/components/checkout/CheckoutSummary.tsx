import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { CartItem } from '@/lib/types/ecommerce';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils';

interface CheckoutSummaryProps {
  cartItems: CartItem[];
  subtotal: number;
  currency: string;
  onProceed: () => void;
}

const CheckoutSummary: React.FC<CheckoutSummaryProps> = ({
  cartItems,
  subtotal,
  currency,
  onProceed,
}) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

      <div className="divide-y divide-gray-200">
        {cartItems.map((item) => (
          <div key={item.id} className="py-4 flex items-start">
            {/* Product Image */}
            <div className="flex-shrink-0 w-20 h-20 bg-gray-200 rounded-md overflow-hidden relative">
              {item.product?.images && item.product.images.length > 0 ? (
                <Image
                  src={item.product.images[0].url}
                  alt={item.product?.name || 'Product'}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-500">
                  No image
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="ml-4 flex-1">
              <h3 className="text-base font-medium text-gray-900">
                <Link href={`/products/${item.product?.slug}`} className="hover:text-primary-600">
                  {item.product?.name}
                </Link>
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Sold by: Finder Marketplace
              </p>
              <div className="flex justify-between mt-1">
                <p className="text-sm font-medium text-gray-900">
                  {formatCurrency(item.product?.price || 0, currency)}
                </p>
                <p className="text-sm text-gray-600">
                  Qty: {item.quantity}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 border-t border-gray-200 pt-6">
        <div className="flex justify-between text-base font-medium text-gray-900">
          <p>Subtotal</p>
          <p>{formatCurrency(subtotal, currency)}</p>
        </div>
        <p className="mt-0.5 text-sm text-gray-500">
          Shipping and taxes calculated at checkout.
        </p>
      </div>

      <div className="mt-6">
        <Button
          onClick={onProceed}
          className="w-full bg-primary-600 hover:bg-primary-700"
        >
          Proceed to Payment
        </Button>
      </div>
    </div>
  );
};

export default CheckoutSummary;
