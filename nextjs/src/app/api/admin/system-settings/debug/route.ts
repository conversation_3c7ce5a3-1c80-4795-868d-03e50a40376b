import { NextRequest, NextResponse } from 'next/server';
import { createSPASassClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get the raw record without any filters
    const { data: rawRecord, error: rawError } = await supabase
      .from('system_settings')
      .select('*')
      .eq('setting_key', 'service_fee_config');

    console.log('Raw record:', rawRecord);
    console.log('Raw error:', rawError);

    // Try with is_active = true filter
    const { data: activeRecord, error: activeError } = await supabase
      .from('system_settings')
      .select('*')
      .eq('setting_key', 'service_fee_config')
      .eq('is_active', true);

    console.log('Active record:', activeRecord);
    console.log('Active error:', activeError);

    return NextResponse.json({
      success: true,
      rawRecord,
      rawError: rawError?.message,
      activeRecord,
      activeError: activeError?.message
    });

  } catch (error) {
    console.error('Error debugging system settings:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Update the record to set is_active = true
    const { data: updateData, error: updateError } = await supabase
      .from('system_settings')
      .update({
        is_active: true,
        category: 'fees',
        description: 'Service fee configuration for transactions'
      })
      .eq('setting_key', 'service_fee_config')
      .select();

    if (updateError) {
      return NextResponse.json({
        success: false,
        message: 'Failed to update record',
        error: updateError.message
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Record updated successfully',
      data: updateData
    });

  } catch (error) {
    console.error('Error updating system settings:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}