'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Trash2, Minus, Plus, ShoppingBag, User } from 'lucide-react';
import { useToast } from '@/lib/hooks/use-toast';
import { formatCurrency } from '@/utils';

import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import { Button } from '@/components/ui/button';
import { useCart } from '@/lib/context/CartContext';
import { createSPASassClient } from '@/lib/supabase/client';

export default function CartPage() {
  const { cartItems, loading, updateCartItem, removeFromCart } = useCart();
  const { toast } = useToast();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [guestCartCount, setGuestCartCount] = useState(0);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      const client = await createSPASassClient();
      const { data: { user } } = await client.getSupabaseClient().auth.getUser();
      setIsAuthenticated(!!user);

      if (!user) {
        // Check guest cart
        const localCart = JSON.parse(localStorage.getItem('guest_cart') || '[]');
        setGuestCartCount(localCart.length);
      }
    } catch (error) {
      console.error('Error checking authentication:', error);
      setIsAuthenticated(false);
    }
  };

  // Helper function to get effective price (considering flash sale prices)
  const getEffectivePrice = (product: any) => {
    // Check if product has a flash sale (compare_at_price exists and is greater than current price)
    if (product?.compare_at_price && product.compare_at_price > product.price) {
      return product.price; // Already discounted price
    }
    return product?.price || 0;
  };

  // Calculate cart totals
  const subtotal = cartItems.reduce((total, item) => {
    return total + getEffectivePrice(item.product) * item.quantity;
  }, 0);

  const currency = cartItems[0]?.product?.currency || 'GMD';





  const handleUpdateQuantity = async (cartItemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;

    try {
      await updateCartItem(cartItemId, newQuantity);

      toast({
        title: 'Cart updated',
        description: 'Your cart has been updated successfully.',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error updating cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to update your cart. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleRemoveItem = async (cartItemId: string) => {
    try {
      await removeFromCart(cartItemId);

      toast({
        title: 'Item removed',
        description: 'The item has been removed from your cart.',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error removing item from cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove the item from your cart. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Show login prompt for unauthenticated users with items in guest cart
  if (isAuthenticated === false && guestCartCount > 0) {
    return (
      <EcommerceLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <ShoppingBag className="mx-auto h-16 w-16 text-primary-600 mb-4" />
            <h1 className="text-3xl font-bold mb-4">Sign in to view your cart</h1>
            <p className="text-gray-600 mb-2">
              You have {guestCartCount} {guestCartCount === 1 ? 'item' : 'items'} in your cart.
            </p>
            <p className="text-gray-600 mb-8">
              Please sign in to view and manage your cart items.
            </p>
            <div className="space-x-4">
              <Link href="/auth/login">
                <Button className="bg-primary-600 hover:bg-primary-700">
                  <User className="w-4 h-4 mr-2" />
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button variant="outline">
                  Create Account
                </Button>
              </Link>
            </div>
            <div className="mt-6">
              <Link href="/products">
                <Button variant="ghost" className="text-primary-600 hover:text-primary-700">
                  Continue Shopping
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  return (
    <EcommerceLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Your Cart</h1>

        {loading || isAuthenticated === null ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        ) : cartItems.length === 0 ? (
          <div className="text-center py-12">
            <ShoppingBag className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h2 className="text-2xl font-semibold mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Looks like you haven't added any products to your cart yet.</p>
            <Link href="/products">
              <Button className="bg-primary-600 hover:bg-primary-700">
                Continue Shopping
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Cart Items ({cartItems.length})</h2>

                  <div className="divide-y divide-gray-200">
                    {cartItems.map((item) => (
                      <div key={item.id} className="py-6 flex flex-col sm:flex-row">
                        {/* Product Image */}
                        <div className="flex-shrink-0 w-full sm:w-24 h-24 mb-4 sm:mb-0">
                          {(() => {
                            // Helper function to get the image URL
                            const getImageUrl = () => {
                              if (!item.product?.images || item.product.images.length === 0) {
                                return null;
                              }
                              
                              const firstImage = item.product.images[0];
                              
                              // Handle different image URL formats
                              if (typeof firstImage === 'string') {
                                return firstImage;
                              }
                              
                              if (typeof firstImage === 'object' && firstImage !== null) {
                                return firstImage.url || firstImage.image_url || null;
                              }
                              
                              return null;
                            };

                            const imageUrl = getImageUrl();

                            return imageUrl ? (
                              <Image
                                src={imageUrl}
                                alt={item.product?.name || 'Product image'}
                                width={96}
                                height={96}
                                className="w-full h-full object-cover rounded-md"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const fallback = target.parentElement?.querySelector('.image-fallback') as HTMLElement;
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                  }
                                }}
                              />
                            ) : null;
                          })()}
                          
                          {/* Show placeholder if no image URL or if product is null */}
                          {(!item.product?.images || item.product.images.length === 0 || !item.product) && (
                            <div className="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
                              <span className="text-gray-500 text-sm">No image</span>
                            </div>
                          )}
                          
                          {/* Fallback for broken images */}
                          <div className="image-fallback hidden w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
                            <span className="text-gray-500 text-sm">Image unavailable</span>
                          </div>
                        </div>

                        {/* Product Details */}
                        <div className="flex-1 sm:ml-6">
                          <div className="flex flex-col sm:flex-row sm:justify-between">
                            <div>
                              <h3 className="text-base font-medium text-gray-900">
                                <Link href={`/products/${item.product?.slug}`} className="hover:text-primary-600">
                                  {item.product?.name}
                                </Link>
                              </h3>
                              <p className="mt-1 text-sm text-gray-500">
                                Sold by: Finder Marketplace
                              </p>
                              <div className="mt-1">
                                {item.product?.compare_at_price && item.product.compare_at_price > item.product.price ? (
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm font-medium text-red-600">
                                      {formatCurrency(item.product.price, currency)}
                                    </span>
                                    <span className="text-sm text-gray-500 line-through">
                                      {formatCurrency(item.product.compare_at_price, currency)}
                                    </span>
                                    <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                      {Math.round(((item.product.compare_at_price - item.product.price) / item.product.compare_at_price) * 100)}% OFF
                                    </span>
                                  </div>
                                ) : (
                                  <p className="text-sm font-medium text-gray-900">
                                    {formatCurrency(getEffectivePrice(item.product), currency)}
                                  </p>
                                )}
                              </div>
                            </div>

                            <div className="mt-4 sm:mt-0 flex items-center">
                              {/* Quantity Controls */}
                              <div className="flex items-center border border-gray-300 rounded-md">
                                <button
                                  onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                                  className="p-2 text-gray-600 hover:text-gray-900"
                                  disabled={item.quantity <= 1}
                                >
                                  <Minus className="h-4 w-4" />
                                </button>
                                <span className="w-10 text-center">{item.quantity}</span>
                                <button
                                  onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                                  className="p-2 text-gray-600 hover:text-gray-900"
                                >
                                  <Plus className="h-4 w-4" />
                                </button>
                              </div>

                              {/* Remove Button */}
                              <button
                                onClick={() => handleRemoveItem(item.id)}
                                className="ml-4 text-red-500 hover:text-red-700"
                                aria-label="Remove item"
                              >
                                <Trash2 className="h-5 w-5" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden sticky top-24">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">{formatCurrency(subtotal, currency)}</span>
                    </div>

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between font-semibold">
                        <span>Total</span>
                        <span>{formatCurrency(subtotal, currency)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <Link href="/checkout-new">
                      <Button className="w-full bg-primary-600 hover:bg-primary-700">
                        Proceed to Checkout
                      </Button>
                    </Link>

                    <Link href="/products">
                      <Button variant="outline" className="w-full mt-3">
                        Continue Shopping
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
