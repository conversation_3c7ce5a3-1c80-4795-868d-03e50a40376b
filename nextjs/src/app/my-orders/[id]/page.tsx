'use client';

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import { useUserOrder } from '@/features/orders/queries';
import { formatCurrency } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import OrderStatusIndicator from '@/components/checkout/OrderStatusIndicator';
import OrderRatingButton from '@/components/orders/OrderRatingButton';
import { 
  ArrowLeft,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle
} from 'lucide-react';

export default function OrderDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { data: order, isLoading, error } = useUserOrder(id as string);

  if (isLoading) {
    return (
      <EcommerceLayout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/3" />
              <div className="h-64 bg-gray-200 rounded" />
              <div className="h-48 bg-gray-200 rounded" />
            </div>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  if (error || !order) {
    return (
      <EcommerceLayout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Order not found
              </h3>
              <p className="text-gray-500 mb-6">
                The order you're looking for doesn't exist or you don't have permission to view it.
              </p>
              <Button onClick={() => router.push('/my-orders')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Orders
              </Button>
            </div>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  const getStatusIcon = () => {
    switch (order.status) {
      case 'pending':
        return <Clock className="h-5 w-5" />;
      case 'processing':
        return <Package className="h-5 w-5" />;
      case 'shipped':
        return <Truck className="h-5 w-5" />;
      case 'delivered':
        return <CheckCircle className="h-5 w-5" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5" />;
      case 'refunded':
        return <AlertCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStatusColor = () => {
    switch (order.status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'refunded':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <EcommerceLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => router.push("/my-orders")}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Button>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Order #{order.id.substring(0, 8)}
                </h1>
                <div className="flex items-center text-gray-500 mt-1">
                  <Calendar className="h-4 w-4 mr-1" />
                  Placed on {new Date(order.created_at).toLocaleDateString()}
                </div>
              </div>

              <Badge
                variant="outline"
                className={`${getStatusColor()} flex items-center gap-2 text-sm px-3 py-1`}
              >
                {getStatusIcon()}
                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Order Status */}
              <Card>
                <CardHeader>
                  <CardTitle>Order Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <OrderStatusIndicator status={order.status} />
                </CardContent>
              </Card>

              {/* Rating Section for Delivered Orders */}
              <OrderRatingButton 
                orderId={order.id}
              />

              {/* Order Items */}
              <Card>
                <CardHeader>
                  <CardTitle>Order Items ({order.items_count})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.items?.map((item, index) => (
                      <div key={item.id}>
                        <div className="flex items-start gap-4">
                          <div className="h-16 w-16 bg-gray-100 rounded overflow-hidden relative flex-shrink-0">
                            {item.product?.images &&
                            item.product.images.length > 0 ? (
                              <Image
                                src={item.product.images[0].url}
                                alt={item.product.name}
                                fill
                                className="object-cover"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full w-full text-gray-400">
                                <Package className="h-8 w-8" />
                              </div>
                            )}
                          </div>

                          <div className="flex-1">
                            <div className="flex justify-between">
                              <div>
                                <h4 className="font-medium">
                                  {item.product?.name ||
                                    `Product ID: ${item.product_id}`}
                                </h4>
                                <p className="text-sm text-gray-500">
                                  Sold by Finder Marketplace
                                </p>
                                <p className="text-sm text-gray-500">
                                  Qty: {item.quantity} ×{" "}
                                  {formatCurrency(item.price, order.currency)}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-medium">
                                  {formatCurrency(item.total, order.currency)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        {index < (order.items?.length || 0) - 1 && (
                          <Separator className="mt-4" />
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Order Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>{formatCurrency(order.total, order.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    {/* <span>Delivery</span> */}
                    <div className="space-y-2">
                      {order.delivery_method === "pickup" ? (
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-green-600" />
                          <span className="font-medium">Store Pickup</span>
                        </div>
                      ) : order.delivery_method === "delivery" ? (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">
                              {order.delivery_service?.name
                                ? `Delivery via ${order.delivery_service.name}`
                                : "Home Delivery"}
                            </span>
                          </div>
                          {order.delivery_service?.estimated_delivery_time && (
                            <p className="text-sm text-gray-600 ml-6">
                              Estimated delivery time:{" "}
                              {order.delivery_service.estimated_delivery_time}
                            </p>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-500">
                            Delivery method not specified
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>{formatCurrency(order.total, order.currency)}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Shipping Information */}
              {order.shipping_address && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Shipping Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {order.shipping_name && (
                      <p className="font-medium">{order.shipping_name}</p>
                    )}
                    <p>{order.shipping_address}</p>
                    {order.shipping_city && (
                      <p>
                        {[
                          order.shipping_city,
                          order.shipping_state,
                          order.shipping_postal_code,
                          order.shipping_country,
                        ]
                          .filter(Boolean)
                          .join(", ")}
                      </p>
                    )}
                    {order.shipping_phone && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="h-4 w-4" />
                        {order.shipping_phone}
                      </div>
                    )}
                    {order.shipping_email && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Mail className="h-4 w-4" />
                        {order.shipping_email}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Additional Notes */}
              {order.notes && (
                <Card>
                  <CardHeader>
                    <CardTitle>Order Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600">{order.notes}</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </EcommerceLayout>
  );
}
