'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, ShoppingCart, Star } from 'lucide-react';
import { Product } from '../types';
import { formatCurrency } from '@/lib/utils';
import { useToast } from '@/lib/hooks/use-toast';
import { useCart } from '@/lib/context/CartContext';

interface ProductCardProps {
  product: Product;
  className?: string;
  hideStoreInfo?: boolean; // Hide store information when already in store context
}

export function ProductCard({ product, className = '', hideStoreInfo = true }: ProductCardProps) {
  const { toast } = useToast();
  const { addToCart } = useCart();
  const {
    id,
    name,
    slug,
    price,
    compareAtPrice,
    currency,
    images,
    store,
    rating,
    reviewCount,
  } = product;

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      // Use the addToCart method from the cart context
      await addToCart(id, 1);
      toast({
        title: "Added to cart",
        description: `${name} has been added to your cart.`,
        variant: "success",
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddToWishlist = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      // Import and use wishlist service
      const { createSPASassClient } = await import('@/lib/supabase/client');
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        toast({
          title: "Sign in required",
          description: "Please sign in to add items to your wishlist.",
          variant: "default",
        });
        return;
      }

      const { error } = await supabase
        .from('wishlist')
        .insert({
          user_id: user.id,
          product_id: id
        });

      if (error && error.code !== '23505') { // Ignore unique constraint violations
        throw error;
      }

      toast({
        title: "Added to wishlist",
        description: `${name} has been added to your wishlist.`,
        variant: "success",
      });
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      toast({
        title: "Error",
        description: "Failed to add item to wishlist. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle both mock data format and regular format
  const discount = compareAtPrice ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100) : 0;

  // Handle different image formats (database vs mock data)
  let imageUrl = '/placeholder-product.jpg';

  // First try to get image from the images array (from product_images table)
  if (images && images.length > 0) {
    imageUrl = images[0].url;
  }
  // Then try the imageUrl property (mock data format)
  else if ('imageUrl' in product && product.imageUrl) {
    imageUrl = product.imageUrl as string;
  }
  // Then try image_url property (direct field in products table)
  else if ('image_url' in product && product.image_url) {
    imageUrl = product.image_url as string;
  }



  return (
    <div className={`group relative bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow ${className}`}>
      {/* Discount badge */}
      {discount > 0 && (
        <div className="absolute top-2 left-2 z-10 bg-red-500 text-white text-xs font-medium px-2 py-1 rounded">
          {discount}% OFF
        </div>
      )}
      
      {/* Local/International badge */}
      <div className={`absolute top-2 ${discount > 0 ? 'left-16' : 'left-2'} z-10 ${product.isLocal ? 'bg-green-500' : 'bg-blue-500'} text-white text-xs font-medium px-2 py-1 rounded`}>
        {product.isLocal ? 'Local' : 'International'}
      </div>

      {/* Wishlist button */}
      <button
        onClick={handleAddToWishlist}
        className="absolute top-2 right-2 z-10 bg-white/80 p-1.5 rounded-full text-gray-600 hover:text-red-500 transition-colors"
        aria-label="Add to wishlist"
      >
        <Heart className="w-4 h-4" />
      </button>

      {/* Product image */}
      <Link href={`/products/${slug}`} className="block relative h-48 md:h-56 lg:h-64 bg-gray-100">
        <Image
          src={imageUrl}
          alt={name}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
          priority
          onError={(e) => {
            // Fallback to placeholder if image fails to load
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-product.jpg';
          }}
        />
      </Link>

      {/* Product info */}
      <div className="p-4">
        {/* Store name if available and not hidden */}
        {!hideStoreInfo && store && (
          <Link href={`/stores/${store.slug}`} className="text-xs text-gray-500 hover:text-primary-600 mb-1 block">
            {store.name}
          </Link>
        )}
        {/* Handle mock data store format - only show if not hidden */}
        {!hideStoreInfo && !store && 'storeId' in product && product.storeId && (
          <Link href={`/stores/${product.storeId}`} className="text-xs text-gray-500 hover:text-primary-600 mb-1 block">
            Store ID: {product.storeId}
          </Link>
        )}

        {/* Product name */}
        <Link href={`/products/${slug}`} className="block">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1 hover:text-primary-600 transition-colors">
            {name}
          </h3>
        </Link>

        {/* Rating */}
        {rating > 0 && (
          <div className="flex items-center mb-1">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    (i + 1) <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500 ml-1">
              {rating.toFixed(1)} ({reviewCount} reviews)
            </span>
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between mt-2">
          <div>
            <span className="text-sm font-medium text-gray-900">
              {formatCurrency(price, currency)}
            </span>
            {compareAtPrice && compareAtPrice > price && (
              <span className="text-xs text-gray-500 line-through ml-1">
                {formatCurrency(compareAtPrice, currency)}
              </span>
            )}
          </div>

          {/* Add to cart button */}
          <button
            className="p-1.5 bg-primary-50 rounded-full text-primary-600 hover:bg-primary-100 transition-colors"
            aria-label="Add to cart"
            onClick={handleAddToCart}
          >
            <ShoppingCart className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
