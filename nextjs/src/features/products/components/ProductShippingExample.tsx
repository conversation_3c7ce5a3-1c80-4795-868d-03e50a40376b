'use client';

import { useState } from 'react';
import { ProductShipping } from '../types';
import { ShippingSelector } from './ShippingSelector';
import { ShippingInfo } from './ShippingInfo';

/**
 * Example component demonstrating how to use the new shipping functionality
 * This shows how to integrate shipping selection and display in your product forms/pages
 */
export function ProductShippingExample() {
  const [productShipping, setProductShipping] = useState<ProductShipping>({
    shippingType: 'local'
  });

  const handleShippingChange = (shipping: ProductShipping) => {
    setProductShipping(shipping);
    console.log('Shipping updated:', shipping);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Product Shipping Configuration
        </h2>
        <p className="text-gray-600 mb-6">
          This example shows how to implement the local/international shipping system.
        </p>
      </div>

      {/* Shipping Selector */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Configure Shipping
        </h3>
        <ShippingSelector
          currentShipping={productShipping}
          onShippingChange={handleShippingChange}
        />
      </div>

      {/* Shipping Info Display */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Shipping Information Preview
        </h3>
        
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Compact View:</h4>
            <ShippingInfo shipping={productShipping} compact />
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Detailed View:</h4>
            <ShippingInfo shipping={productShipping} />
          </div>
        </div>
      </div>

      {/* JSON Debug */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Current Shipping Data:
        </h3>
        <pre className="text-sm text-gray-700 bg-white p-3 rounded border overflow-auto">
          {JSON.stringify(productShipping, null, 2)}
        </pre>
      </div>

      {/* Usage Instructions */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">
          How to Use This System:
        </h3>
        <div className="space-y-3 text-sm text-blue-800">
          <div>
            <strong>1. Product Creation/Editing:</strong>
            <p>Use the ShippingSelector component in your product forms to let users choose between local delivery and international courier services.</p>
          </div>
          <div>
            <strong>2. Product Display:</strong>
            <p>Use the ShippingInfo component to display shipping information on product cards and detail pages.</p>
          </div>
          <div>
            <strong>3. Filtering:</strong>
            <p>Use the new filter options in useProducts hook to filter by shipping type, delivery service, or courier service.</p>
          </div>
          <div>
            <strong>4. Database Schema:</strong>
            <p>You'll need to create the following tables in your database:</p>
            <ul className="list-disc list-inside ml-4 mt-1">
              <li>delivery_services</li>
              <li>courier_services</li>
              <li>product_shipping</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}