#!/bin/bash

# Clean Next.js cache script
echo "Cleaning Next.js cache..."

# Remove .next directory
if [ -d ".next" ]; then
    rm -rf .next
    echo "✅ Removed .next directory"
fi

# Clean npm cache
npm cache clean --force
echo "✅ Cleaned npm cache"

# Clean yarn cache if yarn is being used
if command -v yarn &> /dev/null; then
    yarn cache clean
    echo "✅ Cleaned yarn cache"
fi

echo "🎉 Cache cleanup complete!"