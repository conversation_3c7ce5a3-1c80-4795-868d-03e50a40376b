./README.md: ASCII text
./add_delivery_features.sql: ASCII text
./add_delivery_fee_column.sql: ASCII text
./check_database_structure.sql: ASCII text
./codebase_files.txt: ASCII text
./codebase_index.txt: empty
./components.json: JSON data
./create_enhanced_payout_tables.sql: ASCII text
./create_payout_system_step_by_step.sql: ASCII text
./enhanced_order_system.sql: ASCII text
./eslint.config.mjs: Java source, ASCII text
./fix_notifications_and_orders.sql: ASCII text
./fix_user_role_issue.sql: ASCII text
./fix_user_role_simple.sql: ASCII text
./next-env.d.ts: ASCII text
./next.config.js: ASCII text
./next.config.ts: Java source, ASCII text
./order_workflow_schema.sql: ASCII text
./package-lock.json: JSON data
./package.json: JSON data
./postcss.config.mjs: ASCII text
./public/file.svg: SVG Scalable Vector Graphics image
./public/globe.svg: SVG Scalable Vector Graphics image
./public/next.svg: SVG Scalable Vector Graphics image
./public/placeholder-category.jpg: exported SGML document text, ASCII text
./public/placeholder-product.jpg: exported SGML document text, ASCII text
./public/placeholder-store-logo.png: ASCII text, with very long lines (3284)
./public/terms/privacy-notice.md: ASCII text
./public/terms/refund-policy.md: ASCII text
./public/terms/terms-of-service.md: Unicode text, UTF-8 text
./public/vercel.svg: SVG Scalable Vector Graphics image
./public/window.svg: SVG Scalable Vector Graphics image
./scripts/APPLY_TO_SUPABASE_CLOUD.md: ASCII text
./scripts/README.md: ASCII text
./scripts/add_dashboard_functions.sql: ASCII text
./scripts/add_role_column.sql: ASCII text
./scripts/apply-migrations.js: c program text, ASCII text
./scripts/apply-schema-to-cloud.js: c program text, Unicode text, UTF-8 text
./scripts/check_policies.sql: ASCII text
./scripts/check_products_schema.sql: ASCII text
./scripts/check_role.sql: ASCII text
./scripts/check_uuid_issues.sql: ASCII text
./scripts/complete_setup.sql: ASCII text
./scripts/debug-store-owner-data.sql: ASCII text
./scripts/fix-admin-policies.sql: ASCII text
./scripts/fix-policies.sql: ASCII text
./scripts/fix_cart_schema.sql: ASCII text
./scripts/fix_code.sh: Bourne-Again shell script text executable, ASCII text
./scripts/fix_infinite_recursion.sql: ASCII text
./scripts/fix_order_store_items_rls.sql: ASCII text
./scripts/fix_rls_policies.sql: ASCII text
./scripts/fix_typescript_issues.md: ASCII text
./scripts/multi_role_system.sql: ASCII text
./scripts/setup-store-owner-test-data.sql: ASCII text
./scripts/setup_roles.sql: ASCII text
./scripts/update_inventory.sql: ASCII text
./scripts/verify_user_role.sql: ASCII text
./src/app/account/addresses/page.tsx: Java source, ASCII text
./src/app/account/layout.tsx: Java source, ASCII text
./src/app/account/page.tsx: Java source, ASCII text
./src/app/admin/categories/[id]/edit/page.tsx: Java source, ASCII text
./src/app/admin/categories/new/page.tsx: Java source, ASCII text
./src/app/admin/categories/page.tsx: Java source, Unicode text, UTF-8 text
./src/app/admin/deals/[id]/edit/page.tsx: Java source, ASCII text
./src/app/admin/deals/[id]/page.tsx: Java source, ASCII text
./src/app/admin/deals/new/page.tsx: Java source, ASCII text
./src/app/admin/deals/page.tsx: Java source, Unicode text, UTF-8 text
./src/app/admin/delivery-services/[id]/edit/page.tsx: Java source, ASCII text
./src/app/admin/delivery-services/[id]/page.tsx: Java source, ASCII text
./src/app/admin/delivery-services/new/page.tsx: Java source, ASCII text
./src/app/admin/delivery-services/page.tsx: Java source, ASCII text
./src/app/admin/layout.tsx: Java source, ASCII text
./src/app/admin/orders/[id]/edit/page.tsx: Java source, ASCII text
./src/app/admin/orders/[id]/page.tsx: Java source, Unicode text, UTF-8 text
./src/app/admin/orders/page.tsx: Java source, ASCII text
./src/app/admin/page.tsx: HTML document text, Unicode text, UTF-8 text
./src/app/admin/payouts/page.tsx: Java source, ASCII text
./src/app/admin/products/[id]/edit/page.tsx: Java source, ASCII text
./src/app/admin/products/new/page.tsx: Java source, ASCII text
./src/app/admin/products/page.tsx: Java source, ASCII text
./src/app/admin/settings/page.tsx: Java source, ASCII text
./src/app/admin/stores/[id]/edit/page.tsx: Java source, ASCII text
./src/app/admin/stores/[id]/page.tsx: Java source, Unicode text, UTF-8 text
./src/app/admin/stores/new/page.tsx: Java source, ASCII text
./src/app/admin/stores/page.tsx: Java source, ASCII text
./src/app/admin/synergy-store/page.tsx: Java source, ASCII text
./src/app/admin/users/[id]/edit/page.tsx: Java source, ASCII text
./src/app/admin/users/page.tsx: Java source, ASCII text
./src/app/api/admin/categories/route.ts: Java source, ASCII text
./src/app/api/admin/csv-upload/route.ts: Java source, ASCII text
./src/app/api/admin/delivery-services/[id]/route.ts: Java source, ASCII text
./src/app/api/admin/delivery-services/route.ts: Java source, ASCII text
./src/app/api/admin/orders/[id]/route.ts: Java source, ASCII text
./src/app/api/admin/orders/route.ts: Java source, ASCII text
./src/app/api/admin/product-images/route.ts: C++ source text, ASCII text
./src/app/api/admin/products/route.ts: Java source, ASCII text
./src/app/api/admin/stores/route.ts: Java source, ASCII text
./src/app/api/admin/users/[id]/route.ts: Java source, ASCII text
./src/app/api/admin/users/route.ts: Java source, ASCII text
./src/app/api/auth/callback/route.ts: Java source, ASCII text
./src/app/app/storage/page.tsx: Java source, ASCII text
./src/app/app/table/page.tsx: Java source, ASCII text
./src/app/auth/2fa/page.tsx: Java source, ASCII text
./src/app/auth/forgot-password/page.tsx: Java source, ASCII text
./src/app/auth/layout.tsx: Java source, ASCII text
./src/app/auth/login/page.tsx: Java source, ASCII text
./src/app/auth/register/page.tsx: Java source, ASCII text
./src/app/auth/reset-password/page.tsx: Java source, ASCII text
./src/app/auth/verify-email/page.tsx: Java source, ASCII text
./src/app/cart/page.tsx: Java source, ASCII text
./src/app/categories/[slug]/page.tsx: Java source, ASCII text
./src/app/categories/page.tsx: Java source, ASCII text
./src/app/checkout/page.tsx: Java source, ASCII text
./src/app/deals/page.tsx: Java source, Unicode text, UTF-8 text
./src/app/favicon.ico: MS Windows icon resource - 3 icons, 16x16, 32 bits/pixel, 32x32, 32 bits/pixel
./src/app/globals.css: ASCII text
./src/app/layout.tsx: HTML document text, ASCII text
./src/app/legal/[document]/page.tsx: Java source, ASCII text
./src/app/legal/layout.tsx: Java source, ASCII text
./src/app/legal/page.tsx: Java source, ASCII text
./src/app/my-orders/[id]/page.tsx: Java source, Unicode text, UTF-8 text
./src/app/my-orders/page.tsx: Java source, ASCII text
./src/app/notifications/page.tsx: Java source, Unicode text, UTF-8 text
./src/app/page.tsx: Java source, ASCII text
./src/app/products/[slug]/page.tsx: Java source, ASCII text
./src/app/products/page.tsx: Java source, ASCII text
./src/app/stores/[slug]/page.tsx: Java source, ASCII text
./src/app/stores/page.tsx: Java source, ASCII text
./src/app/wishlist/page.tsx: Java source, ASCII text
./src/components/AuthAwareButtons.tsx: Java source, ASCII text
./src/components/Confetti.tsx: Java source, ASCII text
./src/components/Cookies.tsx: Java source, ASCII text
./src/components/HomePricing.tsx: Java source, ASCII text
./src/components/LegalDocument.tsx: Java source, ASCII text
./src/components/LegalDocuments.tsx: Java source, ASCII text
./src/components/MFASetup.tsx: Java source, ASCII text
./src/components/MFAVerification.tsx: Java source, ASCII text
./src/components/SSOButtons.tsx: Java source, ASCII text, with very long lines (787)
./src/components/admin/AdminHeader.tsx: Java source, ASCII text
./src/components/admin/AdminLayout.tsx: Java source, ASCII text
./src/components/admin/CSVUpload.tsx: Java source, Unicode text, UTF-8 text
./src/components/admin/DataTable.tsx: HTML document text, Unicode text, UTF-8 text
./src/components/admin/ImageUpload.tsx: C++ source text, ASCII text
./src/components/admin/PayoutDetailsModal.tsx: Java source, ASCII text
./src/components/admin/StatsCard.tsx: Java source, Unicode text, UTF-8 text
./src/components/admin/StatusBadge.tsx: Java source, ASCII text
./src/components/admin/StoreOwnerPayoutCard.tsx: Java source, Unicode text, UTF-8 text
./src/components/admin/data-table.tsx: HTML document text, Unicode text, UTF-8 text
./src/components/admin/index.ts: ASCII text
./src/components/checkout/AddressConfirmation.tsx: Java source, Unicode text, UTF-8 text
./src/components/checkout/CheckoutSummary.tsx: Java source, ASCII text
./src/components/checkout/CryptoPayment.tsx: Java source, ASCII text
./src/components/checkout/DeliveryMethodSelector.tsx: Java source, ASCII text
./src/components/checkout/EnhancedCheckoutSummary.tsx: Java source, Unicode text, UTF-8 text
./src/components/checkout/OrderStatusIndicator.tsx: Java source, ASCII text
./src/components/checkout/PaymentMethodSelector.tsx: Java source, ASCII text
./src/components/checkout/ShippingAddressForm.tsx: Java source, ASCII text
./src/components/checkout/TransactionVerification.tsx: Java source, ASCII text
./src/components/checkout/WavePayment.tsx: Java source, ASCII text
./src/components/ecommerce/Deals.tsx: Java source, ASCII text
./src/components/ecommerce/EcommerceLayout.tsx: Java source, ASCII text
./src/components/ecommerce/Footer.tsx: Java source, ASCII text
./src/components/ecommerce/Header.tsx: Java source, Unicode text, UTF-8 text
./src/components/ecommerce/Hero.tsx: Java source, ASCII text
./src/components/ecommerce/Newsletter.tsx: Java source, ASCII text
./src/components/ecommerce/TrendingProducts.tsx: Java source, ASCII text
./src/components/notifications/NotificationDropdown.tsx: Java source, Unicode text, UTF-8 text
./src/components/profile/ProfileForm.tsx: Java source, ASCII text
./src/components/ui/alert-dialog.tsx: ASCII text, with very long lines (518)
./src/components/ui/alert.tsx: ASCII text
./src/components/ui/badge.tsx: ASCII text
./src/components/ui/button.tsx: ASCII text, with very long lines (349)
./src/components/ui/card.tsx: ASCII text
./src/components/ui/checkbox.tsx: ASCII text, with very long lines (311)
./src/components/ui/dialog.tsx: ASCII text, with very long lines (518)
./src/components/ui/input.tsx: ASCII text, with very long lines (395)
./src/components/ui/label.tsx: ASCII text
./src/components/ui/pagination.tsx: Java source, ASCII text
./src/components/ui/progress.tsx: ASCII text
./src/components/ui/radio-group.tsx: ASCII text
./src/components/ui/select.tsx: ASCII text, with very long lines (462)
./src/components/ui/separator.tsx: ASCII text
./src/components/ui/skeleton.tsx: ASCII text
./src/components/ui/switch.tsx: ASCII text, with very long lines (379)
./src/components/ui/table.tsx: HTML document text, ASCII text
./src/components/ui/textarea.tsx: ASCII text, with very long lines (316)
./src/components/ui/toast.tsx: ASCII text, with very long lines (606)
./src/components/ui/toaster.tsx: ASCII text
./src/features/admin/api.ts: Java source, ASCII text
./src/features/admin/types.ts: ASCII text
./src/features/auth/api.ts: Java source, ASCII text
./src/features/auth/types.ts: Java source, ASCII text
./src/features/categories/api-client.ts: Java source, ASCII text
./src/features/categories/api.ts: Java source, ASCII text
./src/features/categories/components/CategoryCard.tsx: Java source, ASCII text
./src/features/categories/components/CategoryGrid.tsx: Java source, ASCII text
./src/features/categories/components/FeaturedCategories.tsx: Java source, ASCII text
./src/features/categories/components/index.ts: ASCII text
./src/features/categories/index.ts: ASCII text
./src/features/categories/queries.ts: Java source, ASCII text
./src/features/categories/types.ts: Java source, ASCII text
./src/features/orders/README.md: Java source, Unicode text, UTF-8 text
./src/features/orders/api.ts: Java source, ASCII text
./src/features/orders/components/OrderCard.tsx: Java source, ASCII text
./src/features/orders/components/OrderList.tsx: HTML document text, ASCII text
./src/features/orders/components/OrderStats.tsx: Java source, ASCII text
./src/features/orders/components/index.ts: ASCII text
./src/features/orders/queries.ts: Java source, ASCII text
./src/features/products/api-client.ts: Java source, ASCII text
./src/features/products/api.ts: Java source, ASCII text
./src/features/products/components/FeaturedProducts.tsx: Java source, ASCII text
./src/features/products/components/ProductCard.tsx: Java source, ASCII text, with very long lines (375)
./src/features/products/components/ProductDetails.tsx: Java source, ASCII text
./src/features/products/components/ProductGrid.tsx: Java source, ASCII text
./src/features/products/components/index.ts: ASCII text
./src/features/products/index.ts: ASCII text
./src/features/products/queries.ts: Java source, ASCII text
./src/features/products/types.ts: ASCII text
./src/features/storage/api.ts: Java source, ASCII text
./src/features/storage/types.ts: ASCII text
./src/features/stores/api-client.ts: Java source, ASCII text
./src/features/stores/api.ts: Java source, ASCII text
./src/features/stores/components/DirectFeaturedStores.tsx: Java source, ASCII text
./src/features/stores/components/FeaturedStores.tsx: Java source, ASCII text
./src/features/stores/components/StoreCard.tsx: Java source, ASCII text
./src/features/stores/components/StoreGrid.tsx: Java source, ASCII text
./src/features/stores/components/index.ts: ASCII text
./src/features/stores/index.ts: ASCII text
./src/features/stores/queries.ts: Java source, ASCII text
./src/features/stores/types.ts: Java source, ASCII text
./src/features/user/api.ts: Java source, ASCII text
./src/features/user/queries.ts: Java source, ASCII text
./src/features/user/types.ts: ASCII text
./src/lib/context/CartContext.tsx: Java source, ASCII text
./src/lib/context/GlobalContext.tsx: Java source, ASCII text
./src/lib/hooks/use-toast.ts: ASCII text
./src/lib/hooks/useAuth.ts: Java source, ASCII text
./src/lib/mock-data/categories.ts: Java source, ASCII text
./src/lib/mock-data/index.ts: ASCII text
./src/lib/mock-data/mock-service.ts: Java source, ASCII text
./src/lib/mock-data/products.ts: Java source, ASCII text
./src/lib/mock-data/stores.ts: Java source, ASCII text
./src/lib/mock-data/types.ts: ASCII text
./src/lib/pricing.ts: C++ source text, ASCII text
./src/lib/providers/QueryProvider.tsx: Java source, ASCII text
./src/lib/services/checkout.ts: Java source, ASCII text
./src/lib/services/deals.ts: Java source, ASCII text
./src/lib/services/ecommerce-client.ts: Java source, ASCII text
./src/lib/services/ecommerce.ts: Java source, ASCII text
./src/lib/services/feature-toggles.ts: Java source, ASCII text
./src/lib/services/notification-client.ts: Java source, ASCII text
./src/lib/services/notification.ts: Java source, ASCII text
./src/lib/services/payout.ts: Java source, ASCII text
./src/lib/supabase/client.ts: Java source, ASCII text
./src/lib/supabase/middleware.ts: ASCII text
./src/lib/supabase/server.ts: Java source, ASCII text
./src/lib/supabase/serverAdminClient.ts: Java source, ASCII text
./src/lib/supabase/unified.ts: Java source, ASCII text
./src/lib/types.ts: C++ source text, ASCII text
./src/lib/types/ecommerce.ts: ASCII text
./src/lib/types/roles.ts: ASCII text
./src/lib/utils.ts: ASCII text
./src/lib/utils/format.ts: ASCII text
./src/lib/utils/string.ts: ASCII text
./src/lib/utils/user.ts: Java source, ASCII text
./src/middleware.ts: ASCII text
./supabase/migrations/20240101000000_create_get_order_details_function.sql: ASCII text
./supabase/migrations/20240601000000_fix_get_order_details_function.sql: ASCII text
./supabase/migrations/20240602000000_fix_profile_policies.sql: ASCII text
./supabase/migrations/20240603000000_fix_ambiguous_product_id.sql: ASCII text
./tailwind.config.ts: Java source, ASCII text
./test_order_workflow.md: ASCII text
./tsconfig.json: JSON data
./update_roles_to_admin_user_only.sql: ASCII text
./yarn.lock: ASCII text
