./README.md
./add_delivery_features.sql
./add_delivery_fee_column.sql
./check_database_structure.sql
./codebase_files.txt
./components.json
./create_enhanced_payout_tables.sql
./create_payout_system_step_by_step.sql
./enhanced_order_system.sql
./eslint.config.mjs
./fix_notifications_and_orders.sql
./fix_user_role_issue.sql
./fix_user_role_simple.sql
./next-env.d.ts
./next.config.js
./next.config.ts
./order_workflow_schema.sql
./package-lock.json
./package.json
./postcss.config.mjs
./public/file.svg
./public/globe.svg
./public/next.svg
./public/placeholder-category.jpg
./public/placeholder-product.jpg
./public/placeholder-store-logo.png
./public/terms/privacy-notice.md
./public/terms/refund-policy.md
./public/terms/terms-of-service.md
./public/vercel.svg
./public/window.svg
./scripts/APPLY_TO_SUPABASE_CLOUD.md
./scripts/README.md
./scripts/add_dashboard_functions.sql
./scripts/add_role_column.sql
./scripts/apply-migrations.js
./scripts/apply-schema-to-cloud.js
./scripts/check_policies.sql
./scripts/check_products_schema.sql
./scripts/check_role.sql
./scripts/check_uuid_issues.sql
./scripts/complete_setup.sql
./scripts/debug-store-owner-data.sql
./scripts/fix-admin-policies.sql
./scripts/fix-policies.sql
./scripts/fix_cart_schema.sql
./scripts/fix_code.sh
./scripts/fix_infinite_recursion.sql
./scripts/fix_order_store_items_rls.sql
./scripts/fix_rls_policies.sql
./scripts/fix_typescript_issues.md
./scripts/multi_role_system.sql
./scripts/setup-store-owner-test-data.sql
./scripts/setup_roles.sql
./scripts/update_inventory.sql
./scripts/verify_user_role.sql
./src/app/account/addresses/page.tsx
./src/app/account/layout.tsx
./src/app/account/page.tsx
./src/app/admin/categories/[id]/edit/page.tsx
./src/app/admin/categories/new/page.tsx
./src/app/admin/categories/page.tsx
./src/app/admin/deals/[id]/edit/page.tsx
./src/app/admin/deals/[id]/page.tsx
./src/app/admin/deals/new/page.tsx
./src/app/admin/deals/page.tsx
./src/app/admin/delivery-services/[id]/edit/page.tsx
./src/app/admin/delivery-services/[id]/page.tsx
./src/app/admin/delivery-services/new/page.tsx
./src/app/admin/delivery-services/page.tsx
./src/app/admin/layout.tsx
./src/app/admin/orders/[id]/edit/page.tsx
./src/app/admin/orders/[id]/page.tsx
./src/app/admin/orders/page.tsx
./src/app/admin/page.tsx
./src/app/admin/payouts/page.tsx
./src/app/admin/products/[id]/edit/page.tsx
./src/app/admin/products/new/page.tsx
./src/app/admin/products/page.tsx
./src/app/admin/settings/page.tsx
./src/app/admin/stores/[id]/edit/page.tsx
./src/app/admin/stores/[id]/page.tsx
./src/app/admin/stores/new/page.tsx
./src/app/admin/stores/page.tsx
./src/app/admin/synergy-store/page.tsx
./src/app/admin/users/[id]/edit/page.tsx
./src/app/admin/users/page.tsx
./src/app/api/admin/categories/route.ts
./src/app/api/admin/csv-upload/route.ts
./src/app/api/admin/delivery-services/[id]/route.ts
./src/app/api/admin/delivery-services/route.ts
./src/app/api/admin/orders/[id]/route.ts
./src/app/api/admin/orders/route.ts
./src/app/api/admin/product-images/route.ts
./src/app/api/admin/products/route.ts
./src/app/api/admin/stores/route.ts
./src/app/api/admin/users/[id]/route.ts
./src/app/api/admin/users/route.ts
./src/app/api/auth/callback/route.ts
./src/app/app/storage/page.tsx
./src/app/app/table/page.tsx
./src/app/auth/2fa/page.tsx
./src/app/auth/forgot-password/page.tsx
./src/app/auth/layout.tsx
./src/app/auth/login/page.tsx
./src/app/auth/register/page.tsx
./src/app/auth/reset-password/page.tsx
./src/app/auth/verify-email/page.tsx
./src/app/cart/page.tsx
./src/app/categories/[slug]/page.tsx
./src/app/categories/page.tsx
./src/app/checkout/page.tsx
./src/app/deals/page.tsx
./src/app/favicon.ico
./src/app/globals.css
./src/app/layout.tsx
./src/app/legal/[document]/page.tsx
./src/app/legal/layout.tsx
./src/app/legal/page.tsx
./src/app/my-orders/[id]/page.tsx
./src/app/my-orders/page.tsx
./src/app/notifications/page.tsx
./src/app/page.tsx
./src/app/products/[slug]/page.tsx
./src/app/products/page.tsx
./src/app/stores/[slug]/page.tsx
./src/app/stores/page.tsx
./src/app/wishlist/page.tsx
./src/components/AuthAwareButtons.tsx
./src/components/Confetti.tsx
./src/components/Cookies.tsx
./src/components/HomePricing.tsx
./src/components/LegalDocument.tsx
./src/components/LegalDocuments.tsx
./src/components/MFASetup.tsx
./src/components/MFAVerification.tsx
./src/components/SSOButtons.tsx
./src/components/admin/AdminHeader.tsx
./src/components/admin/AdminLayout.tsx
./src/components/admin/CSVUpload.tsx
./src/components/admin/DataTable.tsx
./src/components/admin/ImageUpload.tsx
./src/components/admin/PayoutDetailsModal.tsx
./src/components/admin/StatsCard.tsx
./src/components/admin/StatusBadge.tsx
./src/components/admin/StoreOwnerPayoutCard.tsx
./src/components/admin/data-table.tsx
./src/components/admin/index.ts
./src/components/checkout/AddressConfirmation.tsx
./src/components/checkout/CheckoutSummary.tsx
./src/components/checkout/CryptoPayment.tsx
./src/components/checkout/DeliveryMethodSelector.tsx
./src/components/checkout/EnhancedCheckoutSummary.tsx
./src/components/checkout/OrderStatusIndicator.tsx
./src/components/checkout/PaymentMethodSelector.tsx
./src/components/checkout/ShippingAddressForm.tsx
./src/components/checkout/TransactionVerification.tsx
./src/components/checkout/WavePayment.tsx
./src/components/ecommerce/Deals.tsx
./src/components/ecommerce/EcommerceLayout.tsx
./src/components/ecommerce/Footer.tsx
./src/components/ecommerce/Header.tsx
./src/components/ecommerce/Hero.tsx
./src/components/ecommerce/Newsletter.tsx
./src/components/ecommerce/TrendingProducts.tsx
./src/components/notifications/NotificationDropdown.tsx
./src/components/profile/ProfileForm.tsx
./src/components/ui/alert-dialog.tsx
./src/components/ui/alert.tsx
./src/components/ui/badge.tsx
./src/components/ui/button.tsx
./src/components/ui/card.tsx
./src/components/ui/checkbox.tsx
./src/components/ui/dialog.tsx
./src/components/ui/input.tsx
./src/components/ui/label.tsx
./src/components/ui/pagination.tsx
./src/components/ui/progress.tsx
./src/components/ui/radio-group.tsx
./src/components/ui/select.tsx
./src/components/ui/separator.tsx
./src/components/ui/skeleton.tsx
./src/components/ui/switch.tsx
./src/components/ui/table.tsx
./src/components/ui/textarea.tsx
./src/components/ui/toast.tsx
./src/components/ui/toaster.tsx
./src/features/admin/api.ts
./src/features/admin/types.ts
./src/features/auth/api.ts
./src/features/auth/types.ts
./src/features/categories/api-client.ts
./src/features/categories/api.ts
./src/features/categories/components/CategoryCard.tsx
./src/features/categories/components/CategoryGrid.tsx
./src/features/categories/components/FeaturedCategories.tsx
./src/features/categories/components/index.ts
./src/features/categories/index.ts
./src/features/categories/queries.ts
./src/features/categories/types.ts
./src/features/orders/README.md
./src/features/orders/api.ts
./src/features/orders/components/OrderCard.tsx
./src/features/orders/components/OrderList.tsx
./src/features/orders/components/OrderStats.tsx
./src/features/orders/components/index.ts
./src/features/orders/queries.ts
./src/features/products/api-client.ts
./src/features/products/api.ts
./src/features/products/components/FeaturedProducts.tsx
./src/features/products/components/ProductCard.tsx
./src/features/products/components/ProductDetails.tsx
./src/features/products/components/ProductGrid.tsx
./src/features/products/components/index.ts
./src/features/products/index.ts
./src/features/products/queries.ts
./src/features/products/types.ts
./src/features/storage/api.ts
./src/features/storage/types.ts
./src/features/stores/api-client.ts
./src/features/stores/api.ts
./src/features/stores/components/DirectFeaturedStores.tsx
./src/features/stores/components/FeaturedStores.tsx
./src/features/stores/components/StoreCard.tsx
./src/features/stores/components/StoreGrid.tsx
./src/features/stores/components/index.ts
./src/features/stores/index.ts
./src/features/stores/queries.ts
./src/features/stores/types.ts
./src/features/user/api.ts
./src/features/user/queries.ts
./src/features/user/types.ts
./src/lib/context/CartContext.tsx
./src/lib/context/GlobalContext.tsx
./src/lib/hooks/use-toast.ts
./src/lib/hooks/useAuth.ts
./src/lib/mock-data/categories.ts
./src/lib/mock-data/index.ts
./src/lib/mock-data/mock-service.ts
./src/lib/mock-data/products.ts
./src/lib/mock-data/stores.ts
./src/lib/mock-data/types.ts
./src/lib/pricing.ts
./src/lib/providers/QueryProvider.tsx
./src/lib/services/checkout.ts
./src/lib/services/deals.ts
./src/lib/services/ecommerce-client.ts
./src/lib/services/ecommerce.ts
./src/lib/services/feature-toggles.ts
./src/lib/services/notification-client.ts
./src/lib/services/notification.ts
./src/lib/services/payout.ts
./src/lib/supabase/client.ts
./src/lib/supabase/middleware.ts
./src/lib/supabase/server.ts
./src/lib/supabase/serverAdminClient.ts
./src/lib/supabase/unified.ts
./src/lib/types.ts
./src/lib/types/ecommerce.ts
./src/lib/types/roles.ts
./src/lib/utils.ts
./src/lib/utils/format.ts
./src/lib/utils/string.ts
./src/lib/utils/user.ts
./src/middleware.ts
./supabase/migrations/20240101000000_create_get_order_details_function.sql
./supabase/migrations/20240601000000_fix_get_order_details_function.sql
./supabase/migrations/20240602000000_fix_profile_policies.sql
./supabase/migrations/20240603000000_fix_ambiguous_product_id.sql
./tailwind.config.ts
./test_order_workflow.md
./tsconfig.json
./update_roles_to_admin_user_only.sql
./yarn.lock
