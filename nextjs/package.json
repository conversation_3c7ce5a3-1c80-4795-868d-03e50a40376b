{"name": "finder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "./clean-cache.sh", "clean-cache": "rm -rf .next && npm cache clean --force"}, "dependencies": {"@next/third-parties": "^15.1.5", "@paddle/paddle-js": "^1.3.3", "@paddle/paddle-node-sdk": "^2.3.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.4", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.10", "@tanstack/react-query": "^5.28.0", "@tanstack/react-query-devtools": "^5.28.0", "@vercel/analytics": "^1.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.0.2", "lucide-react": "^0.469.0", "next": "15.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "recharts": "^2.15.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}