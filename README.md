# Finder - E-commerce Marketplace Platform

A comprehensive multi-vendor e-commerce marketplace built with Next.js 15, Supabase, and modern web technologies.

## 📖 Complete Documentation

For comprehensive project documentation, please see:
**[FINDER_PROJECT_DOCUMENTATION.md](./FINDER_PROJECT_DOCUMENTATION.md)**

This document contains:
- Complete project overview and features
- Technology stack details
- Database schema and structure
- Development guidelines and conventions
- Admin system documentation
- Recent fixes and updates
- Deployment instructions

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd finder
   ```

2. **Install dependencies**
   ```bash
   # Root dependencies
   npm install
   
   # Next.js app dependencies
   cd nextjs
   npm install
   ```

3. **Set up Supabase**
   ```bash
   # Login to Supabase
   npx supabase login
   
   # Link to your project
   npx supabase link
   
   # Apply migrations
   npx supabase migrations up --linked
   ```

4. **Configure environment**
   ```bash
   cd nextjs
   cp .env.template .env.local
   # Edit .env.local with your Supabase credentials
   ```

5. **Run development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## 🏗️ Project Structure

```
finder/
├── nextjs/                    # Main Next.js application
│   ├── src/
│   │   ├── app/              # Next.js App Router pages
│   │   ├── components/       # Reusable UI components
│   │   ├── features/         # Feature-based modules
│   │   ├── lib/             # Utilities and services
│   │   └── types/           # TypeScript definitions
│   └── public/              # Static assets
├── supabase/                # Database and backend
│   ├── migrations/          # Database migrations
│   ├── scripts/            # Database scripts
│   └── *.sql               # SQL schema files
└── FINDER_PROJECT_DOCUMENTATION.md  # Complete docs
```

## ✨ Key Features

- **Multi-vendor Marketplace**: Support for multiple stores and vendors
- **Advanced Product Management**: Categories, filtering, search, and deals
- **User Management**: Authentication, profiles, and role-based access
- **Shopping Experience**: Cart, wishlist, checkout, and order tracking
- **Payment Processing**: Wave QR codes and cryptocurrency support
- **Admin Dashboard**: Comprehensive admin panel with feature toggles
- **Real-time Updates**: Live notifications and order status updates

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL), Next.js API Routes
- **State Management**: TanStack React Query
- **Authentication**: Supabase Auth
- **UI Components**: shadcn/ui, Lucide React
- **Payments**: Wave, Cryptocurrency

## 📚 Documentation

- **[Complete Documentation](./FINDER_PROJECT_DOCUMENTATION.md)** - Comprehensive project guide
- **[Next.js App Documentation](./nextjs/README.md)** - Frontend-specific documentation
- **[Database Scripts](./supabase/scripts/README.md)** - Database utilities and deployment
- **[Legal Documents](./nextjs/public/terms/)** - Privacy policy, terms of service, refund policy

## 🚀 Deployment

### Database Setup
```bash
# Apply all SQL schemas
node supabase/scripts/apply-schema-to-cloud.js

# Run migrations
node supabase/scripts/apply-migrations.js
```

### Frontend Deployment
The Next.js app can be deployed to Vercel, Netlify, or any platform supporting Next.js.

See [deployment documentation](./FINDER_PROJECT_DOCUMENTATION.md#deployment--scripts) for detailed instructions.

## 🔧 Development

### Currency
- Default currency: **Dalasis (GMD)**

### Code Organization
- Feature-based folder structure
- TypeScript for type safety
- Consistent API response format
- TanStack React Query for data fetching

### Admin System
- Unified admin interface at `/admin`
- Feature toggles for role-based access
- Support for both admins and store owners

## 📝 Recent Updates

- ✅ **File Organization**: All SQL and script files organized in `/supabase/`
- ✅ **Deal Deletion Fix**: Resolved issue with deleted deals still showing on frontend
- ✅ **Admin Consolidation**: Unified admin interface with feature toggles
- ✅ **Documentation**: Comprehensive project documentation created

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the Apache License - see the [LICENSE](./LICENSE) file for details.

---

**For detailed information about the project, architecture, and development guidelines, please refer to [FINDER_PROJECT_DOCUMENTATION.md](./FINDER_PROJECT_DOCUMENTATION.md)**