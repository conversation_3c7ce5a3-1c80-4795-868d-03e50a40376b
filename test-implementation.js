// Test script to verify the local vs international products implementation
// Run this in your browser console or as a Node.js script

async function testImplementation() {
  console.log('🧪 Testing Local vs International Products Implementation...\n');

  // Test 1: Check if courier services admin page loads
  console.log('1. Testing Courier Services Admin Interface...');
  try {
    const response = await fetch('/admin/courier-services');
    if (response.ok) {
      console.log('✅ Courier services admin page accessible');
    } else {
      console.log('❌ Courier services admin page not accessible:', response.status);
    }
  } catch (error) {
    console.log('❌ Error accessing courier services admin:', error.message);
  }

  // Test 2: Check if courier services API works
  console.log('\n2. Testing Courier Services API...');
  try {
    const response = await fetch('/api/admin/courier-services');
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Courier services API working');
      console.log(`   Found ${data.data?.length || 0} courier services`);
      if (data.data?.length > 0) {
        console.log(`   Sample service: ${data.data[0].name}`);
      }
    } else {
      console.log('❌ Courier services API error:', response.status);
    }
  } catch (error) {
    console.log('❌ Error calling courier services API:', error.message);
  }

  // Test 3: Check if products API includes isLocal property
  console.log('\n3. Testing Products API Enhancement...');
  try {
    const response = await fetch('/api/products?limit=5');
    if (response.ok) {
      const data = await response.json();
      const products = data.products?.data || [];
      console.log('✅ Products API accessible');
      console.log(`   Found ${products.length} products`);
      
      if (products.length > 0) {
        const hasIsLocal = products.some(p => p.hasOwnProperty('isLocal'));
        if (hasIsLocal) {
          console.log('✅ Products include isLocal property');
          const localCount = products.filter(p => p.isLocal).length;
          const intlCount = products.filter(p => !p.isLocal).length;
          console.log(`   Local products: ${localCount}, International: ${intlCount}`);
        } else {
          console.log('❌ Products missing isLocal property');
        }
      }
    } else {
      console.log('❌ Products API error:', response.status);
    }
  } catch (error) {
    console.log('❌ Error calling products API:', error.message);
  }

  // Test 4: Check if shipping type filtering works
  console.log('\n4. Testing Shipping Type Filter...');
  try {
    const localResponse = await fetch('/api/products?shippingType=local&limit=3');
    const intlResponse = await fetch('/api/products?shippingType=international&limit=3');
    
    if (localResponse.ok && intlResponse.ok) {
      const localData = await localResponse.json();
      const intlData = await intlResponse.json();
      
      console.log('✅ Shipping type filtering works');
      console.log(`   Local filter returned ${localData.products?.data?.length || 0} products`);
      console.log(`   International filter returned ${intlData.products?.data?.length || 0} products`);
    } else {
      console.log('❌ Shipping type filtering failed');
    }
  } catch (error) {
    console.log('❌ Error testing shipping filters:', error.message);
  }

  // Test 5: Verify database tables exist (requires direct DB access)
  console.log('\n5. Database Schema Verification...');
  console.log('ℹ️  To verify database tables:');
  console.log('   - Check if courier_services table exists');
  console.log('   - Check if product_shipping table exists');
  console.log('   - Verify sample data was inserted');
  console.log('   - Run: SELECT * FROM courier_services LIMIT 3;');

  console.log('\n🎉 Implementation test completed!');
  console.log('\nNext steps:');
  console.log('1. Run the database setup script: database-setup.sql');
  console.log('2. Visit /admin/courier-services to add services');
  console.log('3. Visit /products to see local/international badges');
  console.log('4. Test the shipping type filters');
}

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
  testImplementation();
} else {
  console.log('Run testImplementation() to test the implementation');
}

// Export for Node.js use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testImplementation };
}// Test script to verify the local vs international products implementation
// Run this in your browser console or as a Node.js script

async function testImplementation() {
  console.log('🧪 Testing Local vs International Products Implementation...\n');

  // Test 1: Check if courier services admin page loads
  console.log('1. Testing Courier Services Admin Interface...');
  try {
    const response = await fetch('/admin/courier-services');
    if (response.ok) {
      console.log('✅ Courier services admin page accessible');
    } else {
      console.log('❌ Courier services admin page not accessible:', response.status);
    }
  } catch (error) {
    console.log('❌ Error accessing courier services admin:', error.message);
  }

  // Test 2: Check if courier services API works
  console.log('\n2. Testing Courier Services API...');
  try {
    const response = await fetch('/api/admin/courier-services');
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Courier services API working');
      console.log(`   Found ${data.data?.length || 0} courier services`);
      if (data.data?.length > 0) {
        console.log(`   Sample service: ${data.data[0].name}`);
      }
    } else {
      console.log('❌ Courier services API error:', response.status);
    }
  } catch (error) {
    console.log('❌ Error calling courier services API:', error.message);
  }

  // Test 3: Check if products API includes isLocal property
  console.log('\n3. Testing Products API Enhancement...');
  try {
    const response = await fetch('/api/products?limit=5');
    if (response.ok) {
      const data = await response.json();
      const products = data.products?.data || [];
      console.log('✅ Products API accessible');
      console.log(`   Found ${products.length} products`);
      
      if (products.length > 0) {
        const hasIsLocal = products.some(p => p.hasOwnProperty('isLocal'));
        if (hasIsLocal) {
          console.log('✅ Products include isLocal property');
          const localCount = products.filter(p => p.isLocal).length;
          const intlCount = products.filter(p => !p.isLocal).length;
          console.log(`   Local products: ${localCount}, International: ${intlCount}`);
        } else {
          console.log('❌ Products missing isLocal property');
        }
      }
    } else {
      console.log('❌ Products API error:', response.status);
    }
  } catch (error) {
    console.log('❌ Error calling products API:', error.message);
  }

  // Test 4: Check if shipping type filtering works
  console.log('\n4. Testing Shipping Type Filter...');
  try {
    const localResponse = await fetch('/api/products?shippingType=local&limit=3');
    const intlResponse = await fetch('/api/products?shippingType=international&limit=3');
    
    if (localResponse.ok && intlResponse.ok) {
      const localData = await localResponse.json();
      const intlData = await intlResponse.json();
      
      console.log('✅ Shipping type filtering works');
      console.log(`   Local filter returned ${localData.products?.data?.length || 0} products`);
      console.log(`   International filter returned ${intlData.products?.data?.length || 0} products`);
    } else {
      console.log('❌ Shipping type filtering failed');
    }
  } catch (error) {
    console.log('❌ Error testing shipping filters:', error.message);
  }

  // Test 5: Verify database tables exist (requires direct DB access)
  console.log('\n5. Database Schema Verification...');
  console.log('ℹ️  To verify database tables:');
  console.log('   - Check if courier_services table exists');
  console.log('   - Check if product_shipping table exists');
  console.log('   - Verify sample data was inserted');
  console.log('   - Run: SELECT * FROM courier_services LIMIT 3;');

  console.log('\n🎉 Implementation test completed!');
  console.log('\nNext steps:');
  console.log('1. Run the database setup script: database-setup.sql');
  console.log('2. Visit /admin/courier-services to add services');
  console.log('3. Visit /products to see local/international badges');
  console.log('4. Test the shipping type filters');
}

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
  testImplementation();
} else {
  console.log('Run testImplementation() to test the implementation');
}

// Export for Node.js use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testImplementation };
}