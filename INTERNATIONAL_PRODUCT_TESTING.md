# International Product Testing Guide

## What was implemented:

1. **Enhanced DeliveryMethodSelector Component**:
   - Now supports both delivery services (local) and courier services (international)
   - Automatically switches between the two based on the `isInternationalProduct` prop
   - Shows appropriate icons: 🚛 for local delivery, ✈️ for international shipping
   - Displays relevant information like country availability and per-kg pricing for international

2. **Updated Checkout Page**:
   - Detects international products in cart using `!item.product.is_local`
   - Loads appropriate services (delivery services vs courier services) based on product type
   - Shows visual indicator when international products are detected
   - Calculates shipping costs correctly for both service types

## Testing Steps:

### Step 1: Verify Product Data Structure
First, check if your products have the `is_local` field:
```sql
SELECT id, name, is_local FROM products LIMIT 10;
```

### Step 2: Create Test International Product
If you need to create a test international product:
```sql
-- Update an existing product to be international
UPDATE products 
SET is_local = false 
WHERE id = 'YOUR_PRODUCT_ID';
```

### Step 3: Set Up Courier Services
Make sure you have active courier services in your database:
```sql
-- Check existing courier services
SELECT * FROM courier_services WHERE is_active = true;

-- If none exist, create a test one
INSERT INTO courier_services (
  id, name, base_cost, estimated_delivery_time, 
  tracking_supported, countries, is_active
) VALUES (
  gen_random_uuid(), 
  'International Express', 
  50.00, 
  '5-7 business days',
  true,
  ARRAY['USA', 'UK', 'Canada', 'Germany'],
  true
);
```

### Step 4: Test the Flow
1. **Add an international product to cart** (where `is_local = false`)
2. **Go to checkout** (`/checkout-new`)
3. **Look for indicators**:
   - In the cart step, you should see: "✈️ International shipping required - This order contains international products"
   - In the delivery step, you should see:
     - "International Shipping" instead of "Home Delivery"
     - "Choose a Courier Service" instead of "Choose a Delivery Service"
     - Plane icon (✈️) instead of truck icon (🚛)
     - Country availability information
     - Per-kg pricing (if applicable)

### Step 5: Verify API Endpoints
The system will call different endpoints based on product type:
- **Local products**: `/api/admin/delivery-services?is_active=true&per_page=100`
- **International products**: `/api/admin/courier-services?is_active=true&per_page=100`

### Step 6: Test Mixed Cart
If you have both local and international products in the cart:
- The system will prioritize international shipping (if any product is international, the entire order is treated as international)
- This ensures consistent shipping method selection

## Expected Behavior:

### For Local Products:
- Shows "Home Delivery" option with truck icon
- Loads delivery services from `/api/admin/delivery-services`
- Uses `base_fee` for pricing
- Standard delivery terminology

### For International Products:
- Shows "International Shipping" option with plane icon
- Loads courier services from `/api/admin/courier-services`
- Uses `baseCost` for pricing
- Shows country availability
- Shows per-kg pricing (if `costPerKg` is set)
- Shows tracking support status
- International shipping terminology

## Troubleshooting:

### Issue: Still showing "Home Delivery" for international products
**Solution**: Check that:
1. The product has `is_local = false` in the database
2. The cart is properly loading product data with the `is_local` field
3. The `isInternationalOrder` variable is being calculated correctly

### Issue: No courier services showing
**Solution**: Check that:
1. You have active courier services in the database (`is_active = true`)
2. The API endpoint `/api/admin/courier-services` is working
3. The courier services have the correct data structure

### Issue: API endpoints not found
**Solution**: 
1. Verify the courier services API endpoint exists
2. Check if the endpoint follows the same pattern as delivery services
3. Ensure proper permissions and authentication

## Database Schema Requirements:

### Products Table:
```sql
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_local BOOLEAN DEFAULT true;
```

### Courier Services Table:
```sql
CREATE TABLE IF NOT EXISTS courier_services (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  base_cost DECIMAL(10,2) NOT NULL,
  cost_per_kg DECIMAL(10,2),
  estimated_delivery_time VARCHAR,
  tracking_supported BOOLEAN DEFAULT false,
  countries TEXT[], -- Array of country codes/names
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

This implementation allows for a seamless user experience where the shipping method automatically adapts based on the products in the cart.